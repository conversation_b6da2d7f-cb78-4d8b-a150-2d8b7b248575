import * as yup from 'yup';
import { fileValidation, imageSizeValidation } from '../../helpers/validationSchemaHelpers';
/* export const sceneSchema = yup.object({
    sceneName: yup.string().required(),
    sceneType: yup.string().required(),
    lowResolutionFile: yup.mixed().when('sceneType', {
      is: (val:string) => val !== 'earth',
      then: () => yup.mixed().required().
        test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
        .test('lowResolutionFile', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
      otherwise: () => yup.string().nullable(),
    }),
    highResolutionFile: yup.mixed().when('sceneType', {
      is: (val:string) => val !== 'earth' && val !== 'gsplat',
      then: () => yup.mixed().required().
        test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
        .test('highResolutionFile', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
      otherwise: () => yup.string().nullable(),
    }),
    infoText: yup.string().required(),
    parent: yup.string(),
    isActive: yup.boolean(),
    root: yup.boolean(),
    clouds: yup.boolean(),
    building_id: yup.string().when('sceneType', {
      is: (val:string) => val === 'identical_unitplan',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    gsplat: yup.mixed().when('sceneType', {
      is: (val:string) => val === 'gsplat',
      then: () => yup.mixed().required().
        test('is-valid-type', 'Not a valid zip type', (value) => fileValidation(value as File, 'zip')),
      otherwise: () => yup.string().nullable(),
    }),
  }); */

export const sceneSchema = yup.object({
  sceneName: yup.string().required(),
  sceneType: yup.string().required(),
  category: yup.string().nullable(),
  minandmax: yup.array().when('sceneType', {
    is: (val:string) => val === 'deep_zoom',
    then: () => yup.array().required(),
    otherwise: () => yup.array().nullable(),
  }),
  file: yup.mixed().when('sceneType', {
    is: (val:string) => val !== 'earth' && val !== 'gsplat' && val !== 'rotatable_image',
    then: () => yup.mixed().required().test('is-valid-type', 'Upload valid JPG or JPEG file', (value) => fileValidation(value as File, 'sceneImage'))
      .test('file', 'Image size is more than 10 mb', (value) => imageSizeValidation(value  as File)),
    otherwise: () => yup.string().nullable(),
  }),
  infoText: yup.string().optional(),
  parent: yup.string(),
  auto_rotate: yup.boolean(),
  isActive: yup.boolean(),
  root: yup.boolean(),
  clouds: yup.boolean(),
  building_id: yup.string().when('sceneType', {
    is: (val:string) => val === 'identical_unitplan',
    then: () => yup.string().required(),
    otherwise: () => yup.string().when('sceneType', {
      is: (val:string) => val === 'deep_zoom',
      then: () => yup.string().nullable(),
      otherwise: () => yup.string().nullable(),
    }),
  }),
  gsplat: yup.mixed().when('sceneType', {
    is: (val:string) => val === 'gsplat',
    then: () => yup.mixed().required().
      test('is-valid-type', 'Not a valid zip type', (value) => fileValidation(value as File, 'zip')),
    otherwise: () => yup.string().nullable(),
  }),
  gsplat_x: yup.number(),
  gsplat_y: yup.number(),
  gsplat_z: yup.number(),
  polar_angle_max: yup.number().typeError('Polar angle max must be a number'),
  polar_angle_min: yup.number().typeError('Polar angle min must be a number'),
  distance_max: yup.number().typeError('Distance max must be a number'),
  distance_min: yup.number().typeError('Distance min must be a number'),
  // auto_rotate: yup.boolean(),
  // Floor_ids: yup.string().when('sceneType',{
  //     Is:(val) => val === 'identical_unitplan',
  //     Then: () => yup.array().required(),
  //     Otherwise:() => yup.string().nullable(),
  //  }),
});

export const svgSchema = yup.object({
  type: yup.string().required(),
  building_id: yup.string().when('type', {
    is: (val:string) => val === 'floor',
    then: () => yup.string().optional().nullable(),
    otherwise: () => yup.string().nullable(),
  }),
  svgFile: yup.mixed().required().test('is-valid-type', 'Not a valid Svg type', (value) => fileValidation(value as File, 'icon')).test('file', 'Svg size is more than 10 mb', (value) => imageSizeValidation(value  as File)),
});

export const iconSchema = yup.object({
  name: yup.string().required(),
  type: yup.string().required(),
  category: yup.string().required(),
  icon: yup.mixed().required().test('is-valid-type', 'Not a valid Svg type', (value) => fileValidation(value as File, 'icon')).test('file', 'Svg size is more than 10 mb', (value) => imageSizeValidation(value  as File)),
});

export const EditProjectSceneSetting = yup.object({
  name: yup.string().nullable(),
  type: yup.string().nullable(),
  category: yup.string().nullable(),
  minandmax: yup.array().when('type', {
    is: (val:string) => val === 'deep_zoom',
    then: () => yup.array().required(),
    otherwise: () => yup.array().nullable(),
  }),
  file: yup.mixed().when('type', {
    is: (val:string) => val !== 'earth' && val !== 'gsplat' && val !=='rotatable_image',
    then: () =>  yup.mixed().test('is-valid-type', 'Upload valid JPG or JPEG file', (value) => fileValidation(value as File, 'sceneImage'))
      .test('file', 'Image size is more than 10 mb', (value) => imageSizeValidation(value  as File)),
    otherwise: () => yup.string().nullable(),
  }),
  parent: yup.string().nullable(),
  info_text: yup.string().nullable(),
  active: yup.boolean().nullable(),
  root: yup.boolean().nullable(),
  clouds: yup.boolean().nullable(),
  auto_rotate: yup.boolean().nullable(),
  building_id: yup.string().when('type', {
    is: (val:string) => val === 'identical_unitplan',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  floor_ids: yup.array().when('building_id', {
    is: (val:string) => val,
    then: () => yup.array().required()
      .test('floors', 'floor list is Empty', (value) => value && value.length !== 0),
    otherwise: () => yup.string().nullable(),
  }),
  gsplat: yup.mixed().when('type', {
    is: (val:string) => val === 'gsplat',
    then: () => yup.mixed().
      test('is-valid-type', 'Not a valid zip type', (value) => fileValidation(value as File, 'zip')),
    otherwise: () => yup.string().nullable(),
  }),

});

export const generalDesignSettingsSchema = yup.object({
  weblite_visibility: yup.boolean(),
  currency_support: yup.boolean(),
  share_scenes: yup.boolean(),
  svg_visibility: yup.boolean(),
  starting_view: yup.string().when('experience_view', {
    is: (val: string) => val,
    then: () => yup.string().required('Select Starting View'),
    otherwise: () => yup.string().nullable(),
  }),
  experience_view: yup.object().nullable(),
  supported_languages: yup.array().of(yup.object({
    name: yup.string(),
    code: yup.string(),
  })).min(1, 'Select Supported Languages'),
  default_language: yup.array().of(yup.object({
    name: yup.string(),
    code: yup.string(),
  }).required('Select Default Languages')),
});

export const CreateImageFrameSchema = yup.object().shape({
  Frame: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required(),
        file: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
          .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
        thumbnail: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
          .test('thumbnail', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
      }),
    )
    .strict(),
});
