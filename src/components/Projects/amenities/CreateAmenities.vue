<script setup>
import { Field, ErrorMessage, Form } from 'vee-validate';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { apiMediaTypes } from '../../../enum.ts';
import Spinner from '../../common/Spinner.vue';
import { ProjectStore } from '../../../store/project.ts';
import { amenityDataSyncUp, CreateAmenity, getCategories } from '../../../api/projects/amenties';
import Multiselect from 'vue-multiselect';
import { resizeImage } from '../../../helpers/helpers';
import { AmenitySchema } from '../../../validationSchema/amenity';

const projectStore = ProjectStore();
const loader = ref(false);
const route = useRoute();
const projectId = ref(route.params.project_id);
const media_type = ref();
const categoryList = ref([]);
const catRef = ref();
const description = ref();
const initialValues = ref({
  name: '',
  media_type: '',
  category: '',
  community_id: '',
  description: '',
  embed_link: '',
  tour_id: '',
  file: null,
  thumbnail: null,
  isNew: true, // <-- Set this flag dynamically based on context
});

const emit = defineEmits(['close']);

/* Methods */

getCategories(projectId.value).then((res) => { // Get list of categories
  categoryList.value = res.map((elem) => {
    return elem.category;
  });
}).catch((err) => {
  console.log('output->err', err);
});

projectStore.RefreshVirtualTours(projectId.value);
projectStore.RefreshCommunities(projectId.value);

const addTag = (newTag) => { // To add new category if it is not in the
  categoryList.value.push(newTag); // Adding to list
  catRef.value = newTag; // Selecting same new tag
};
/* Submission */
const handleSubmission = async (values) => {
  const typeImage = values.media_type==='image' || values.media_type==='360_image';
  const resizeDimension = values.media_type === '360_image' ? 1200 : 720;
  if (projectId.value) {
    loader.value = true;
    const resizedThumbnail = await resizeImage(typeImage ? values.file : values.thumbnail, resizeDimension, resizeDimension);

    const detailsFormData = new FormData();
    detailsFormData.append('project_id', projectId.value);
    detailsFormData.append('name', values.name);
    detailsFormData.append('category', values.category);
    detailsFormData.append('media_type', values.media_type);
    if (values.file) {
      detailsFormData.append('file', values.file);
    } else {
      detailsFormData.append('file', resizedThumbnail);
    }
    detailsFormData.append('thumbnail', resizedThumbnail);

    if (values.embed_link) {
      detailsFormData.append('embed_link', values.embed_link);
    }
    if (values.tour_id) {
      detailsFormData.append('tour_id', values.tour_id);
    }
    if (values.communityId) {
      detailsFormData.append('community_id', values.communityId);
    }
    values.description && detailsFormData.append('description', values.description);

    await CreateAmenity(
      detailsFormData,
    )
      .then(async () => {
        await amenityDataSyncUp(projectId.value);
        projectStore.ForceRefreshAmenities(projectId.value);
        amenityDataSyncUp(projectId.value);
        emit('close', true);
      })
      .catch(() => {})
      .finally(() => {
        loader.value = false;
      });
  }
};

</script>

<template>
    <div class="modal-content-primary sm:max-w-lg">
      <div>
        <div class="flex justify-between items-center px-3 py-2 border-b border-gray-200">
          <p class="text-sm font-medium text-gray-900">Create Amenity</p>
          <button  class="fill-gray-400" @click="$emit('close',false)">
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
          </button>
        </div>
        <Form :validation-schema="AmenitySchema" :initial-values="initialValues"
                    @submit="handleSubmission" class="w-full mb-0">
            <div class="flex flex-col px-3 py-2">
              <div class="col-span-auto">
                            <label for="name"
                                class="label-primary">
                                Name</label>
                            <Field as="input" type="text"
                                name="name" autocomplete
                                id="name"
                                class="input-primary w-full"
                                :placeholder="`Enter Name`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="name" />
              </div>

              <div
                            class="col-span-auto relative">
                            <label for="category"
                                class="label-primary">Category</label>
                            <div class="mt-2">
                               <Field  name="category" :model-value="catRef" v-slot="{ category }">
                                      <Multiselect
                                      class="!capitalize tabSelect"
                                      v-bind="category"
                                      v-model="catRef"
                                      tag-placeholder="Add this as new category"
                                      placeholder="Search or add Category"
                                      :multiple="false"
                                      :taggable="true"
                                      @tag="addTag"
                                      :allow-empty="false"
                                      :show-labels="false"
                                      :options="categoryList" maxHeight="250" >
                                      </Multiselect>

                                    </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="category" />
                            </div>
              </div>

              <div class="col-span-auto">
                <label for="media_type"
                                class="label-primary">
                  Media Type</label>
                  <Field name="media_type" v-slot="{ field }" :model-value="media_type">
                                <Multiselect v-bind="field" v-model="media_type"
                                    :options="apiMediaTypes" :allow-empty="false"
                                    placeholder="Select" :searchable="false" :close-on-select="true" :show-labels="false"
                                    :multiple="false" class="tabSelect h-fit !capitalize" :maxHeight="150"
                                />
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="media_type" />
              </div>

              <div class="col-span-auto">
                      <label for="communityId"
                          class="label-primary">
                          Communities</label>
                      <Field
                          as="select"
                          name="communityId" id="communityId"
                          autocomplete="communityId"
                      class="select-primary"
                      :placeholder="`Seclect community`">
                      <option value="" disabled>
                          Choose
                      </option>
                      <option value="" disabled
                          v-if="!projectStore.communities">
                          No community found ! </option>
                      <option v-else
                          :value="option._id"
                          v-for="option, index in  projectStore.communities"
                          :key="index"
                          class="text-black">
                          {{ option.name }}
                      </option>
                  </Field>
                  <ErrorMessage as="p"
                      class="text-sm text-rose-500 mt-1"
                      name="communityId" />
              </div>

              <div class="col-span-full mb-2">
                <label for="description" class="label-primary"
                  >Description</label
                >
                <Field
                  name="description"
                  v-model="description"
                  as="textarea"
                  class="textarea-primary"
                  placeholder="Write Description here..."
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="description"
                />
              </div>

              <div class="col-span-auto" v-if="media_type==='embed_link'">
                            <label for="embed_link"
                                class="label-primary">
                                Embed Link</label>
                            <Field as="input" type="url"
                                name="embed_link" autocomplete
                                id="embed_link"
                                class="input-primary w-full"
                                :placeholder="`Enter link`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="embed_link" />
              </div>

              <div class="col-span-auto"  v-if="media_type ==='virtual_tour'">
                            <label for="tour_id"
                                class="label-primary">
                                select virtual Tour</label>
                            <Field
                                as="select" type="text"
                                name="tour_id" id="tour_id"
                                autocomplete="tour_id"
                                class="select-primary"
                                :placeholder="`Seclect Tour`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.virtualtours">
                                    No Tour found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.virtualtours"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.tour_name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="tour_id" />
              </div>

              <div class="col-span-auto" v-if="media_type!=='embed_link' && media_type!=='virtual_tour'">
                            <label for="file"
                                class="label-primary">Upload {{ type }}
                                File</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="file"
                                    id="file"
                                    autocomplete="highRes"
                                    class="input-primary w-full"
                                    placeholder="Upload High Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="file" />
                            </div>
              </div>

            <div
              class="col-span-auto" v-if="media_type !== 'image' && media_type !== '360_image'">
              <label for="thumbnail"
                                class="label-primary">Upload
                  Thumbnail</label>
              <div class="mt-2">
                                <Field type="file"
                                    name="thumbnail"
                                    id="thumbnail"
                                    class="input-primary w-full"
                                    placeholder="Upload Low Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="thumbnail" />
              </div>
            </div>

            <div
              class="text-center pt-3 flex justify-center items-center gap-3"
            >
              <button
                id="submit"
                type="submit"
                class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700"
              >
                Save
                <Spinner v-if="loader" />
              </button>
            </div>
          </div>
        </Form>
      </div>
    </div>
</template>
<style>
.tabSelect .multiselect__content-wrapper{
    position: absolute !important;
}
.tabSelect .multiselect__option , .tabSelect .multiselect__option--selected, .tabSelect .multiselect__option:hover, .tabSelect .multiselect__option--selected:hover{
  background-color: transparent !important;
  color: #6b7280 !important;
  font-weight: 100 !important;
}
.tabSelect .multiselect__option--selected, .tabSelect .multiselect__option--selected:hover{
  color: black !important;
}
.tabSelect>.multiselect__select{
  background: none !important;
}
.tabSelect .multiselect__placeholder{
  color: black !important;
}
.tabSelect.selected-field .multiselect__single {
  background-color: #EBF5FF !important;
}
.tabSelect.selected-field .multiselect__input {
  background-color: #EBF5FF !important;
}
.tabSelect>input{
  background-color: #EBF5FF !important;
}
</style>
