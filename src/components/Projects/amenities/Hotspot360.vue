<script setup>
import { ProjectStore } from '@/store/project';
import { onMounted, onUnmounted, ref } from 'vue';
import Multiselect from 'vue-multiselect';
import Button from '../../common/Button.vue';
import Spinner from '../../common/Spinner.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { tourHotspotSchema } from '@/validationSchema/tour/index';
import { cdn } from '../../../helpers/index.ts';
import CustomTourRightSideBar from '../tours/VR/CustomTourRightSideBar.vue';
import SafeAreaHighlight from '@/components/scenes/SafeAreaHighlight.vue';
import { addLink, deleteLink, updateLink, UpdateAmenity } from '@/api/projects/amenties';

const props = defineProps({
  amenity: {type: Object, default: () => {}},
  listOfItems: {type: Object, default: () => {}},
});
const emits = defineEmits(['updatedAmenity']);
const projectStore =  ProjectStore();
const isTextureLoaded = ref(false);
const SceneTag = ref(null);
const defaultHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspot.svg";
const activeHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspotedit.svg";
const openHotspot = ref(false);
const hotspotToggler = ref({
  isOpen: false,
  type: null,
});
const safeFrame = ref(false);
const hasUserInteracted = ref(false);
const openSettings = ref(false);
const rotationPresent = ref(false);
const dragRotation = ref();
const isDragging = ref(false);
if (!props.amenity.rotation && props.amenity.rotation && props.amenity.rotation !== '0 0 0' && props.amenity.rotation !== '') {
  rotationPresent.value = true;
} else {
  rotationPresent.value = false;
}
const initialHotspotFormData = ref({
  text: null,
  x: null,
  y: null,
  z: null,
  destination_img_id: null,
});
const currentPositions = ref({
  x: null,
  y: null,
  z: null,
});
const hotspotFormLoader = ref({
  delete: false,
  save: false,
});
const selectedDestinationImgId = ref(null);
const currentDraggableElRef = ref(null); // Current Draggable (Edit)
const getTheDblClickPostion = ref(null);
const loader = ref(false);
/* Methods */

// Configure
const setConfigure = () => {
  if (SceneTag.value){
    const camera = document.getElementById('player');
    const aSKy = SceneTag.value.querySelector('a-sky');
    const getRotationDegrees = props?.amenity?.rotation?.split(' '); // XYZ
    console.log("getRotationDegrees", getRotationDegrees);
    if (getRotationDegrees){
      dragRotation.value = {x: Number(getRotationDegrees[0]), y: Number(getRotationDegrees[1]), z: 0};
    }

    if (aSKy && camera){
      const src = cdn(props?.amenity?.file); // get the image url and convert to cdn
      console.log("_inside if");
      const separator = src.includes("?") ? "&" : "?"; // get the separator
      const newSrc = `${src}${separator}t=${new Date().getTime()}`;       // Append a random query parameter to find this src is unique to avoid aframe optimize assets logic
      // set url (360 img)
      aSKy.setAttribute('src', newSrc); // url
      // set orientation
      const lookControls = camera.components["look-controls"];
      if (lookControls && getRotationDegrees) {
        camera.addEventListener('componentinitialized', function (evt) {
          if (evt.detail.name === 'look-controls') {
            const lookControls = camera.components["look-controls"];
            if (lookControls?.pitchObject && getRotationDegrees) {
              lookControls.pitchObject.rotation.x = THREE.Math.degToRad(Number(getRotationDegrees[0]));
              lookControls.pitchObject.rotation.y = THREE.Math.degToRad(Number(getRotationDegrees[1]));
            }
          }
        });
      }
    }
  }
};

// Compare Values
const compareValuesFromSource = (source, compare) => {
  const sourceValues = {...source}; // form values
  const compareValues = {...compare}; // previous values
  let newComparedValues;

  // Check if any values in source are different from compare values
  for (const key in sourceValues) {
    if (sourceValues[key] !== compareValues[key]) {
      console.log(key);

      // different
      if (!newComparedValues){
        newComparedValues = {};
      }

      if (key === 'x' || key === 'y' || key === 'z'){
        console.log("Yes");
        if (!newComparedValues.position) {
          newComparedValues.position = {};
        }
        newComparedValues.position[key] = sourceValues[key];
      } else {
        newComparedValues[key] = sourceValues[key];
      }
    }
  }

  return newComparedValues;
};

// Reset the Hotspot Toggler & Form values (Close/Reset)
const handleCloseHotspotToggler = () => {
  console.log("handleCloseHotspotToggler");
  // Reset the current position, destination_imageId & initial hotspot Data
  initialHotspotFormData.value = {
    text: null,
    x: null,
    y: null,
    z: null,
    destination_img_id: null,
  };
  currentPositions.value = {
    x: null,
    y: null,
    z: null,
  };
  selectedDestinationImgId.value = null;
  getTheDblClickPostion.value = null;
  // Close the hotspot Toggler
  hotspotToggler.value = {
    isOpen: false,
    type: null,
  };
};

// Add Hotspots
const addHotspotIntoViewArea = (addEl, camera_El) => {
  console.log("addHotspotIntoViewArea", addEl, camera_El);

  const hotspotEl = addEl; // add hotspot a-image
  const cameraEl = camera_El; // camera El of scene

  const cameraWorldPosition = new THREE.Vector3();  // Get the camera's world position
  cameraEl.object3D.getWorldPosition(cameraWorldPosition);
  console.log("Camera world position:", cameraWorldPosition);
  const direction = new THREE.Vector3(); // Get direction - either from double-click or camera's current direction

  const basePoint = getTheDblClickPostion.value; // dbl clicked point
  if (basePoint) { // check
    console.log("Create Case", basePoint);
    direction.copy(basePoint); // Use the direction from double-click
  }
  /*  else { // Fallback
    console.log("Else case");

    // Fallback to your original method (camera's current direction)
    cameraEl.object3D.getWorldDirection(direction);
    direction.negate(); // Invert direction so it moves FORWARD from camera (not backward)
    console.log("Using camera direction (fallback):", direction);
  } */

  const cameraNearClippingPlane = 5.0; // Default distance

  const hotspotPosition = cameraWorldPosition.clone().add(direction.multiplyScalar(cameraNearClippingPlane));  // Calculate hotspot position
  console.log("Final hotspot position:", hotspotPosition);

  // Set position
  hotspotEl.setAttribute('position', {
    x: String(hotspotPosition.x),
    y: String(hotspotPosition.y),
    z: String(hotspotPosition.z),
  });

  return hotspotPosition;
};

// Bound to Drag Element to get seemlessly updated positions
const handleTrackDragging = (e) => {
  if (e){
    console.log(e);
    const positions = e.target.object3D.getWorldPosition();
    if (positions){
      currentPositions.value.x = String(positions.x);
      currentPositions.value.y = String(positions.y);
      currentPositions.value.z = String(positions.z);
    }
  }
};

// Add Draggable and set a previous for the current position
const addDraaggableAndUpdate = (element) => {
  console.log('Hotspot clicked!');
  const el = element;
  if (!JSON.parse(el.getAttribute('data-isDragging'))){
    const labelEntity = el.querySelector('.hotspot-label');
    const hotspotId = el.getAttribute('data-Id'); // get the hotspot id
    console.log("isDragging, clicked!");
    el.setAttribute('data-isDragging', 'true');
    currentDraggableElRef.value = el;
    labelEntity.setAttribute('visible', false); // make the label invisible
    el.setAttribute('click-drag', true); // make click & drag
    el.setAttribute('src', "#activeHotspotIcon"); // add the active Hotspot
    el.addEventListener("mouseup", handleTrackDragging);// add event-listener
    // Set all the form values
    hotspotToggler.value = {
      isOpen: true,
      type: 'edit',
    };
    initialHotspotFormData.value = {
      text: props.amenity.links[hotspotId].text,
      x: null,
      y: null,
      z: null,
      destination_img_id: null,
    };
    currentPositions.value = {...props.amenity.links[hotspotId].position};
    selectedDestinationImgId.value = props.listOfItems[props.amenity.links[hotspotId].destination_img_id];
  }
};

// Remove Draggable and Reset the position's for the current u
const removeDraggableAndUpdate = (isClose) => {
  if (currentDraggableElRef.value){ // If the current is only exists, go futher..
    const el = currentDraggableElRef.value;
    const hotspotId = el.getAttribute('data-Id'); // get the hotspot id
    el.setAttribute('data-isDragging', 'false');  // back to default
    if (isClose){ // reset previous positions
      el.setAttribute('position', props.amenity.links[hotspotId].position); // Update entity position instantly
    }
    currentDraggableElRef.value = null; // remove el from reference
    el.removeAttribute('click-drag'); // remove click & drag
    el.removeEventListener('mouseup', handleTrackDragging); // remove event-listener
    el.setAttribute('src', "#defaultHotspotIcon"); // add the default Hotspot
    handleCloseHotspotToggler(); // reset the values
  }
};

if (!AFRAME.components.configure) {
  AFRAME.registerComponent('configure', {
    init: function () {
      console.log("configure", this.el);
      setConfigure(); // Initialize
    },
  });
} else {
  setConfigure();
}

// Hospots Listeners
if (!AFRAME.components['hotspot-listener']) {
  AFRAME.registerComponent('hotspot-listener', {
    init: function () {
      console.log("hotspot-listener", this.el);

      const el = this.el; // element
      const isNewHotspot = JSON.parse(el.getAttribute('data-isNewHotspot')); // check the new-hotspot or existing-hotspot.

      if (isNewHotspot){
        removeDraggableAndUpdate(true); // clear all the existing draggable el and update (This line is just pre-check of any existing)
        const scene = el.sceneEl;
        const cameraEl = scene.camera.el;
        console.log(cameraEl);

        if (cameraEl && el){
          const addHotspotPosition = addHotspotIntoViewArea(el, cameraEl);
          currentPositions.value = {
            x: addHotspotPosition.x,
            y: addHotspotPosition.y,
            z: addHotspotPosition.z,
          };
        }
      } else {

        const labelEntity = el.querySelector('.hotspot-label'); // label entity
        const hotspotId = el.getAttribute('data-Id'); // get the hotspot id

        // Mouse Enter
        el.addEventListener('mouseenter', () => {
          console.log("mouseenter");
          if (labelEntity && !JSON.parse(el.getAttribute('data-isDragging'))) {
            labelEntity.setAttribute('visible', true);
          }
          document.querySelector('a-scene').style.cursor = 'pointer';
          el.setAttribute('opacity', "0.9" );
        });
        // Mouse Leave
        el.addEventListener('mouseleave', () => {
          console.log("mouseleave");
          if (labelEntity && !JSON.parse(el.getAttribute('data-isDragging'))) {
            labelEntity.setAttribute('visible', false);
          }
          document.querySelector('a-scene').style.cursor = 'default';
          el.setAttribute('opacity', "0.5");
        });
        // Click
        el.addEventListener('click', () => {
          console.log(currentDraggableElRef.value);

          if (currentDraggableElRef.value && currentDraggableElRef.value.getAttribute('data-Id') !== hotspotId){
            removeDraggableAndUpdate(true); // clear the existing draggable el (existing hotspots)
            addDraaggableAndUpdate(this.el); // add the drag to the current Elem
          } else {
            console.log("This item (Already click / new click)...", currentDraggableElRef.value, hotspotId);
            if (hotspotToggler.value.isOpen && hotspotToggler.value.type === 'add' && !currentDraggableElRef.value){
              console.log("Yes there new hotspot");
              removeDraggableAndUpdate(true); // clear the new draggable el (new hotspot)
            }
            addDraaggableAndUpdate(this.el); // add the drag to the current Elem
          }
        });

      }
    },
  });
}

// Submit (Hotspot Form) (Add/Update)
const handleSubmit = (val) => {
  hotspotFormLoader.value.save = true; // loader
  if (hotspotToggler.value.type === 'add' ){
    // Add
    console.log("Yes Watched Handle Submit", val);
    const parms = {
      project_id: props.amenity.project_id,
      amenity_id: props.amenity._id,
      position: {
        x: String(val.x),
        y: String(val.y),
        z: String(val.z),
      },
      text: val.text,
      destination_img_id: val.destination_img_id._id,
    };
    console.log(parms);
    // Api call for add the hotspots
    addLink(parms).then((res) => {
      console.log(res, 'Result');
      emits('updatedAmenity', res);
      handleCloseHotspotToggler(); // first, reset
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // then, sync the result to store
      hotspotFormLoader.value.save = false; // reset the loader
    }).catch(() => {
      hotspotFormLoader.value.save = true;
    });
  } else {
    // Edit
    if (currentDraggableElRef.value){
      const hotspotId = currentDraggableElRef.value.getAttribute('data-Id');
      console.log(hotspotId);

      const source = {
        ...val,
        destination_img_id: val.destination_img_id._id,
      };
      const prevData = {
        x: props.amenity.links[hotspotId].position.x,
        y: props.amenity.links[hotspotId].position.y,
        z: props.amenity.links[hotspotId].position.z,
        text: props.amenity.links[hotspotId].text,
        destination_img_id: props.amenity.links[hotspotId].destination_img_id,
      };
      console.log(source);
      console.log(prevData);

      const comparedValues = compareValuesFromSource(source, prevData);

      console.log(comparedValues);

      if (comparedValues){
        const frameParms = {
          project_id: props.amenity.project_id,
          amenity_id: props.amenity._id,
          link_id: hotspotId,
          ...comparedValues,
        };
        console.log(frameParms);
        updateLink(frameParms).then((res) => {
          console.log(res, 'Result');
          removeDraggableAndUpdate(false); // first, remove the draggable & reset the position state's
          // props.listOfItems[res._id] = res;
          emits('updatedAmenity', res);
          hotspotFormLoader.value.save = false; // reset the loader
        }).catch(() => {
          hotspotFormLoader.value.save = true;
        });
      }
    }
  }
};

// Delete the Hotspots
const handleDeleteHotspot = () => {
  console.log("handleDeleteHotspot");

  hotspotFormLoader.value.delete = true; // loader
  if (currentDraggableElRef.value){
    console.log(currentDraggableElRef.value);

    const frameParms = {
      project_id: props.amenity.project_id,
      amenity_id: props.amenity._id,
      link_id: currentDraggableElRef.value.getAttribute('data-Id'),
    };
    console.log(frameParms);
    deleteLink(frameParms).then((res) => {
      console.log("res", res);
      removeDraggableAndUpdate(false); // first, remove the draggable & reset the position state's
      emits('updatedAmenity', res);
      hotspotFormLoader.value.delete = false;
    }).catch(() => {
      hotspotFormLoader.value.delete = true;
    });
  }
};

const handleDblClickInScene = (e) => {
  console.log("handleDblClickInScene", e);

  const scene = SceneTag.value;
  const camera = scene.camera;

  if (!camera) { // If camera is'nt available, then throw a error.
    console.error("camera not found");
    return;
  }

  const rect = scene.canvas.getBoundingClientRect();  // Get canvas bounds for mouse position calculation
  const mouse = new THREE.Vector2(
    (((e.clientX - rect.left) / rect.width) * 2) - 1,
    -(((e.clientY - rect.top) / rect.height) * 2) + 1,
  );

  console.log("Mouse position:", mouse);
  console.log("Canvas rect:", rect);

  const raycaster = new THREE.Raycaster(); // create raycaster to get the direction from camera to clicked point
  raycaster.setFromCamera(mouse, camera);

  const clickDirection = raycaster.ray.direction.clone(); // store the ray direction (this is the direction from camera to clicked point)
  console.log("Click direction:", clickDirection);

  getTheDblClickPostion.value = clickDirection; // store this direction for use

  // Open the create hotspot
  hotspotToggler.value.isOpen = true;
  hotspotToggler.value.type = 'add';
};

const saveStartingView = (data) => {
  loader.value = true;
  console.log("saveStartingView", data);
  const parms = {
    project_id: props.amenity.project_id,
    amenity_id: props.amenity._id,
    rotation: `${data.positionX} ${data.positionY} ${data.positionZ}`,
  };
  console.log(parms);
  UpdateAmenity(parms).then((res) => {
    console.log('Result', res);
    emits('updatedAmenity', res);
    loader.value = false;
  }).catch(() => {
    loader.value = false;
    console.error("Error updating starting view");
  });
};

function filterBy360MediaTypeArray (data, category, currentAmenityId) {
  const filtered = [];

  Object.values(data).forEach((item) => {
    // Check if category matches and media_type contains "360"
    if (item.category === category && item.media_type && item.media_type.includes('360')) {
      // Add disabled property if this item's _id matches current amenity's _id
      const itemWithDisabled = {
        ...item,
        $isDisabled: item._id === currentAmenityId,
      };
      filtered.push(itemWithDisabled);
    }
  });

  return filtered;
}

const handleLookControlsStart = () => {
  if (!hasUserInteracted.value) {
    hasUserInteracted.value = true;
    openSettings.value = true;
  }
};

// Optional: Function to apply rotation to A-Frame camera
const updateCameraRotation = (rotation) => {
  const camera = document.getElementById('player');
  if (camera) {
    const lookControls = camera.components["look-controls"];
    if (lookControls) {
      // Convert degrees to radians and apply
      const polarRad = THREE.Math.degToRad(rotation.x); // X (Vertical) - pitch
      const azimuthRad = THREE.Math.degToRad(rotation.y); // Y (Horizontal) - yaw

      lookControls.pitchObject.rotation.x = polarRad;
      lookControls.yawObject.rotation.y = azimuthRad;

      console.log('Applied rotation to camera:', rotation);
    }
  }
};

const handleRotationUpdate = (newRotation) => {
  console.log('Received rotation update from child:', newRotation);

  // Update the dragRotation ref
  dragRotation.value = {
    x: newRotation.x,
    y: newRotation.y,
    z: newRotation.z,
  };

  // Optional: Apply the rotation to the A-Frame camera immediately
  updateCameraRotation(newRotation);
};

const normalizeRotation = (rotation) => {
  const normalized = { ...rotation };

  // Normalize Y-axis (yaw) to -180 to 180 range
  normalized.y = ((rotation.y % 360) + 360) % 360;
  if (normalized.y > 180) {
    normalized.y -= 360;
  }

  // Normalize X-axis (pitch) to -90 to 90 range
  normalized.x = Math.max(-90, Math.min(90, rotation.x));

  // Z-axis usually stays 0 for look-controls
  normalized.z = 0;

  return normalized;
};

let dragCleanup = null;
onMounted(() => {
  if (SceneTag.value){ // after a-scene created,
    console.log("onMount", SceneTag.value);
    SceneTag.value.addEventListener('dblclick', handleDblClickInScene);
    SceneTag.value.addEventListener('mousedown', handleLookControlsStart);
    SceneTag.value.addEventListener('touchstart', handleLookControlsStart);
    console.log("onMount", SceneTag.value);
    SceneTag.value.addEventListener('dblclick', handleDblClickInScene);
    const setupDragTracking = () => {
      if (SceneTag.value.canvas) {
        // Canvas is ready, set up drag tracking
        const canvas = SceneTag.value.canvas;

        const onDragStart = () => {
          isDragging.value = true;
          console.log("Drag started");
        };

        const onDragMove = () => {
          if (isDragging.value) {
            // Store rotation in temp variable during drag
            const camera = document.getElementById('player');
            if (camera) {
              const rotation = camera.getAttribute('rotation');
              const normalizedRotation = normalizeRotation(rotation);
              window.tempDragRotation = normalizedRotation;
            }
          }
        };

        const onDragEnd = () => {
          if (isDragging.value) {
            isDragging.value = false;
            console.log("Drag ended");

            // Update dragRotation only once when dragging stops
            if (window.tempDragRotation) {
              dragRotation.value = window.tempDragRotation;
              window.tempDragRotation = null;
            }
          }
        };

        canvas.addEventListener('mousedown', onDragStart);
        canvas.addEventListener('mousemove', onDragMove);
        canvas.addEventListener('mouseup', onDragEnd);
        canvas.addEventListener('touchstart', onDragStart);
        canvas.addEventListener('touchmove', onDragMove);
        canvas.addEventListener('touchend', onDragEnd);

        dragCleanup = () => {
          canvas.removeEventListener('mousedown', onDragStart);
          canvas.removeEventListener('mousemove', onDragMove);
          canvas.removeEventListener('mouseup', onDragEnd);
          canvas.removeEventListener('touchstart', onDragStart);
          canvas.removeEventListener('touchmove', onDragMove);
          canvas.removeEventListener('touchend', onDragEnd);
        };
      } else {
        // Canvas not ready, wait for scene to load
        SceneTag.value.addEventListener('loaded', () => {
          setupDragTracking();
        });
      }
    };

    // Try setup immediately or wait for scene loaded
    setupDragTracking();

  }
});

onUnmounted(() => {
  if (SceneTag.value) {
    SceneTag.value.removeEventListener('mousedown', handleLookControlsStart);
    SceneTag.value.removeEventListener('touchstart', handleLookControlsStart);
  }

  if (dragCleanup) {
    dragCleanup();
  }

  // cleanup the register Components
  delete AFRAME.components.configure;
  delete AFRAME.components['hotspot-listener'];
});

</script>

<template>
      <!-- No Tour Found -->
     <div class="w-full h-full flex flex-col justify-center items-center text-center text-white bg-black" v-if="!amenity">
              No VR Tour Found !
     </div>

     <!-- Tour Found -->
    <div v-else class="flex w-full h-full gap-2">
     <div class="h-full w-full flex flex-col">
     <div class="h-full w-full flex justify-center items-center text-white overflow-hidden relative">
      <SafeAreaHighlight :show="safeFrame" :safePadding="{ top: '70px', bottom: '70px', left: '80px', right: '80px' }">
            <img v-if="!isTextureLoaded" :src="cdn(amenity.thumbnail)" alt="" class="w-full h-full absolute top-0 left-0 z-[2] object-cover">
            <!-- AScene Component-->
            <a-scene
             id="VR_scene"
             ref="SceneTag"
             class="aScene_Tour"
             embedded
             loading-screen="enabled:false"
             vr-mode-ui="enabled:false"
             device-orientation-permission-ui="enabled: false"
             renderer="colorManagement:true;sortObjects:true;maxCanvasWidth:1920;maxCanvasHeight:1920;"
            >

              <!-- Mouse Cursor (cursor & raycaster for interactive el) -->
              <a-entity id="mouseCursor" cursor="rayOrigin: mouse;fuse:false;" raycaster="objects: [data-raycastable];" ></a-entity>

              <a-entity id="rig" configure>
                    <!-- Player (Camera) -->
                  <a-entity id="player" camera="far:10000;near:0.5;fov:100" look-controls position="0 0 1e-5"></a-entity>
                    <!-- A-Sky -->
                    <a-sky @materialtextureloaded="isTextureLoaded = true" ></a-sky>
              </a-entity>

              <!-- Assets (Hotspot Icons) -->
              <a-assets>
                    <img id="defaultHotspotIcon" crossorigin="anonymous" :src="defaultHotspotIcon" alt="defaultHotspotIcon"/>
                    <img id="activeHotspotIcon" crossorigin="anonymous" :src="activeHotspotIcon" alt="activeHotspotIcon">
              </a-assets>

              <!-- Hotspots -->
              <!-- Add -->
              <a-image v-if="hotspotToggler.isOpen && hotspotToggler.type === 'add'" look-at="[camera]" src="#activeHotspotIcon" scale="0.8 0.8" position click-drag @mouseup="handleTrackDragging" :data-isNewHotspot="true" data-raycastable hotspot-listener></a-image>

              <!-- Existing -->
              <a-entity v-if="amenity?.links && Object.keys(amenity?.links).length > 0">
                    <a-image :class="[ hotspotToggler.isOpen && hotspotToggler.type === 'add' ? 'pointer-events-none' : 'pointer-events-auto' ]" v-for="link, id in amenity?.links" :key="id" look-at="[camera]" src="#defaultHotspotIcon" :position="link.position" opacity="0.5" scale="0.8 0.8" :data-Id="id" :data-isNewHotspot="false" data-raycastable hotspot-listener :data-isDragging="false">
                        <a-entity
                          class="hotspot-label"
                          position="0 0.9 0"
                          visible="false"
                        >
                            <a-plane
                              width="3"
                              height="0.7"
                              material="color: white;"
                              position="0 0 0"
                            />
                            <a-text
                              :value="link.text"
                              align="center"
                              width="6.9"
                              color="black"
                              position="0 0 0"
                              font="kelsonsans"
                              scale="1.9 1.9"
                            />
                          </a-entity>
                    </a-image>
              </a-entity>

            </a-scene>

            <div
              v-if="isTextureLoaded && !hasUserInteracted"
              class="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-[5] select-none"
            >
              <div class="w-fit h-10 p-4 bg-white rounded-md shadow-[0px_1px_2px_-1px_rgba(0,0,0,0.10)] outline outline-1 outline-gray-300 inline-flex justify-center items-center gap-2.5">
                <div class="w-fit inline-flex flex-col justify-center items-center gap-2.5">
                  <div class="w-fit text-center justify-start text-gray-700 text-sm leading-tight">
                    Click and drag to adjust view, then click Save Starting View.
                  </div>
                </div>
              </div>
            </div>

            <!-- Form (Add/Edit/Delete) -->
            <div v-if="hotspotToggler.isOpen" class="px-2.5 py-2 h-fit absolute right-1 top-[3%] z-[3] bg-white rounded w-64">
              <div class="flex justify-between items-center mb-3">
                <p class="text-black text-base font-semibold"> {{ hotspotToggler.type === 'add' ? 'Add' : 'Update/Delete' }} Hotspot : </p>
                <button v-if="hotspotToggler.type === 'edit'" type="button" class="rotate-45" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false" @click="removeDraggableAndUpdate(true)">
                  <svg class="w-4 h-4" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_308_25527)">
                          <path
                            d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                            fill="red" />
                          <path
                            d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                            fill="red" />
                        </g>
                        <defs>
                          <clipPath id="clip0_308_25527">
                            <rect width="12" height="12" fill="red" />
                          </clipPath>
                        </defs>
                      </svg>
                </button>
              </div>
              <Form :key="hotspotToggler.type === 'add' ? '00' : currentDraggableElRef.getAttribute('data-Id')" class=" flex flex-col justify-start items-end w-full gap-3 mb-0"
                @submit="handleSubmit" :initial-values="initialHotspotFormData" :validation-schema="tourHotspotSchema">

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="long"> Text </label>
                    <Field type="text" name="text" id="text" class="input-primary w-full text-black" placeholder="Enter Text" />
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="text"  />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="x"> X </label>
                    <Field v-model="currentPositions.x" type="text" name="x" id="x" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="x" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="y"> Y </label>
                    <Field  v-model="currentPositions.y" type="text" name="y" id="y" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="y" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="z"> Z </label>
                    <Field v-model="currentPositions.z" type="text" name="z" id="z" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="z" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                  <label class="font-semibold text-sm text-txt-50" for="destination_img_id"> Destination img id </label>
                  <Field name="destination_img_id" :model-value="selectedDestinationImgId" v-slot="{ field }">
                    <Multiselect :allow-empty="false" v-bind="field" v-model="selectedDestinationImgId" :searchable="false" :custom-label="(val) => val.name"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="filterBy360MediaTypeArray(listOfItems,amenity.category,amenity._id)"
                    maxHeight="250">
                    </Multiselect>
                  </Field>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="destination_img_id" />
                    <p v-if="selectedDestinationImgId" class="text-black px-2 py-1"> {{ selectedDestinationImgId.id }} </p>
                </div>

                  <div class="flex justify-end items-center gap-2 w-full h-fit mt-2">
                    <Button v-if="hotspotToggler.type === 'add'" title="Close" type="button" theme="secondary" :disabled="hotspotFormLoader.save" @handleClick="() => handleCloseHotspotToggler()"> </Button>
                    <Button v-else title="Delete" type="button" class="!bg-rose-500" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false" @handleClick="() => handleDeleteHotspot()">
                      <template v-if="hotspotFormLoader.delete" v-slot:svg>
                            <Spinner />
                      </template>
                    </Button>
                    <Button title="Submit" type="submit" theme="primary" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false">
                        <template v-if="hotspotFormLoader.save" v-slot:svg>
                            <Spinner />
                        </template>
                    </Button>
                  </div>
              </Form>
            </div>
      </SafeAreaHighlight>
     </div>
      <div class="h-[90px] shrink-0 w-full flex flex-row justify-end items-center px-3 bg-white">
                <div class="flex justify-between items-center gap-2">
                  <div class="flex justify-between items-center gap-2 rounded-b-md cursor-pointer">
                      <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                          <input id="safe_area" v-model="safeFrame" class="sr-only peer" name="safe_area" type="checkbox" :value="true" />
                          <label for="safe_area" class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:rounded-full after:h-[19px] after:w-[19px] after:transition-all peer-checked:bg-blue-600 cursor-pointer">
                          </label>
                      </div>
                      <label for="safe_area" class="text-sm text-gray-900 mb-0">Safe Frame</label>
                  </div>
                  <svg class="h-4 w-4 fill-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_723_16947)">
                      <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_723_16947">
                       <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                      </clipPath>
                    </defs>
                  </svg>
                </div>
      </div>
    </div>
     <div class="h-full w-[220px] shrink-0">
        <CustomTourRightSideBar :openHotspotForm="openHotspot" :formType="hotspotType" @saveStartingView = "(data) => saveStartingView(data)" :otherFields="trackHotspotDetails" :settings="openSettings" :rotationPresent="rotationPresent" :x="{min:-90, max:90, disable:false}" :y="{min:-180, max:180, disable:false}" :z="{min:0, max:1, disable:true}" :rotation="dragRotation" @rotationUpdate="handleRotationUpdate" :loader="loader"/>
     </div>
    </div>
</template>

<style>
.aScene_Tour .a-canvas{
  z-index: 1 !important;
}
</style>
