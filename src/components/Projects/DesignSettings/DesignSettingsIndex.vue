<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router';

const route = useRoute();
const projectId = ref(route.params.project_id);
const settingItems = ref(['general', 'style']);
const selectedSettingsMenu = ref('general');

if (route.path.includes('/general')) {
  selectedSettingsMenu.value = 'general';
} else if (route.path.includes('/style') ){
  selectedSettingsMenu.value = 'style';
} else {
  selectedSettingsMenu.value = '';
}

watch(() => route.path, (newPath) => {
  if (newPath.includes('/general')) {
    selectedSettingsMenu.value = 'general';
  } else if (newPath.includes('/style')) {
    selectedSettingsMenu.value = 'style';
  } else {
    selectedSettingsMenu.value = '';
  }
});

</script>

<template>
    <div class="w-[175px] h-full bg-white rounded-t-lg p-2 flex flex-col">
        <div class="flex-1 flex flex-col h-full w-full">
            <div class="flex justify-between items-center px-2 w-full">
                <h2 class=" text-gray-500 text-lg font-semibold">
                    Setting
                </h2>
            </div>
            <div class=" flex-1 mt-3 relative z-10 overflow-y-auto">
                <div v-for="(item, index) in settingItems" :key="index">
                    <div class="capitalize cursor-pointer hover:bg-blue-50 p-2 px-2 rounded-md text-sm"
                        :class="[item === selectedSettingsMenu ? 'bg-blue-50 ' : '']"
                        @click="router.push(`/projects/${projectId}/design/settings/${item}`)"> {{ item }}
                    </div>
                </div>
            </div>

        </div>
    </div>

</template>

<style>
.copyMsgBox {
    background-color: #323232;
    color: white;
    position: absolute;
    margin-top: 10px;
    padding: 10px;
    font-size: 14px;
    border-radius: 30px;
    transition: all 900ms linear;

}

.copyMsgBox::before {
    content: '';
    position: absolute;
    left: 22px;
    top: -5px;
    display: block;
    width: 15px;
    height: 15px;
    background-color: #323232;
    border-top-left-radius: 4px;
    transform: rotate(45deg);
}
</style>
