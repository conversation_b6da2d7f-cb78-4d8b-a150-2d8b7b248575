
<script setup>
import {ref} from 'vue';
import {Form, Field, ErrorMessage} from 'vee-validate';
import Multiselect from 'vue-multiselect';
import Button from '@/components/common/Button.vue';
import Spinner from '@/components/common/Spinner.vue';
import {useRoute} from 'vue-router';
import {languages} from '@/helpers/constants';
import { getCookie } from '@/helpers/domhelper';
import { generalDesignSettingsSchema } from '@/validationSchema/scene';
import { ProjectStore } from '../../../store/project';
import { GetProjectById } from '@/api/projects';
import { getAllScenes } from '@/api/masterScene';
import { getListofScenes } from '@/api/projects/scene';
import { truncateString } from '@/helpers/helpers';
import { projectSettingsFormTypes } from '@/enum';
import { UpdateProjectSettings } from '@/api/projects/settings';

const route = useRoute();
const projectId = ref(route.params.project_id);
const current_organization = getCookie('organization');
const org_url_domain = import.meta.env.VITE_UI_LIBRARY_STAG;
const copyString = `${org_url_domain}${current_organization}/projectscene/${projectId.value}`;
const store = ProjectStore();
const loader = ref(false);
const initialData = ref(null);
const previousData = ref(null);
const sceneTypeList = ['project Scene', 'master Scene'];
const initialSceneIdRef = ref(null);
const initialSceneIdList = ref(null);

const getLanguageByCode = (code) => {
  return languages.find((lang) => lang.code === code) || null;
};

const frameParams = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleClipBoardCopy = (msg, copyMsgId) => {

  navigator.clipboard.writeText(msg);
  const messageDOM = document.getElementById(copyMsgId);

  messageDOM.style.visibility = 'visible';
  messageDOM.style.left = '425px';
  messageDOM.style.top = '190px';

  setTimeout(() => {
    messageDOM.style.visibility = 'hidden';
  }, 1000);

};

async function sceneIdApiCallBack (val) {
  return new Promise((resolve, reject) => {
    if (val.split(' ')[0] === 'master') {
      // Master Scene
      getAllScenes().then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
    } else {
      // Projects
      getListofScenes(projectId.value).then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      });
    }
    initialSceneIdRef.value = null;
  });

}

const setupDataCallBack = (values) => {
  console.log("values", values.projectSettings?.ale, values);

  if (values) {
    const data = values;

    // Previous Values
    previousData.value = {
      currency_support: (data.projectSettings?.ale?.currency_support ? data.projectSettings?.ale?.currency_support : false),
      svg_visibility: (data.projectSettings?.ale?.svg_visibility ? data.projectSettings?.ale?.svg_visibility : false),
      weblite_visibility: (data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.weblite_visibility : false),
      share_scenes: (data.projectSettings?.ale?.share_scenes ? data.projectSettings?.ale?.share_scenes : false),
      default_language: (data.projectSettings?.ale?.default_language ? getLanguageByCode(data.projectSettings?.ale?.default_language) : null),
      supported_languages: (data.projectSettings?.ale?.supported_languages ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code)) : null),
      starting_view: (data.projectSettings?.ale?.initial_scene_type ? `${data.projectSettings.ale.initial_scene_type} Scene` : null),
      experience_view: (data.projectSettings?.ale?.initial_scene_id ? data.projectSettings?.ale?.initial_scene_id : null),
    };

    // Initial Values
    initialData.value = {
      currency_support: (data.projectSettings?.ale?.currency_support ? data.projectSettings?.ale?.currency_support : false),
      svg_visibility: (data.projectSettings?.ale?.svg_visibility ? data.projectSettings?.ale?.svg_visibility : false),
      weblite_visibility: (data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.is_enabled : false),
      share_scenes: (data.projectSettings?.ale?.share_scenes ? data.projectSettings?.ale?.share_scenes : false),
      default_language: data.projectSettings?.ale?.default_language
        ? [getLanguageByCode(data.projectSettings.ale.default_language)]
        : [],
      supported_languages: (data.projectSettings?.ale?.supported_languages.length > 0 ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code)).filter(Boolean) : []),
      starting_view: (data.projectSettings?.ale?.initial_scene_type ? `${data.projectSettings.ale.initial_scene_type} Scene` : null),
      experience_view: (data.projectSettings?.ale?.initial_scene_id ? data.projectSettings?.ale?.initial_scene_id : null),
    };

    if (data.projectSettings?.ale?.initial_scene_type) {
      sceneIdApiCallBack(`${data.projectSettings.ale.initial_scene_type} Scene`).then((result) => {
        initialSceneIdRef.value = result[data.projectSettings.ale.initial_scene_id];
      });

    }
  }
};

const saveSettings = async (payload) => {
  return UpdateProjectSettings(payload).then((result) => {
    if (result) {
      loader.value = false;
      store.settings.projectSettings[projectSettingsFormTypes.ALE] = result.projectSettings[projectSettingsFormTypes.ALE];
      setupDataCallBack(result);
      resolve(result);
    }
  });
};

const handleSubmit = async (values) => {
  console.log("submitted Values", values);
  return new Promise(() => {
    loader.value = true;
    const prevData = { ...previousData.value };

    // Changing undefined or null to false for toggle
    if (values.weblite_visibility === undefined || values.weblite_visibility === null) {
      values.weblite_visibility = false;

    }

    if (values.currency_support === undefined || values.currency_support === null) {
      values.currency_support = false;
    }

    if (values.svg_visibility === undefined || values.svg_visibility === null ) {
      values.svg_visibility = false;
    }

    if (values.share_scenes === undefined || values.share_scenes === null ) {
      values.share_scenes = false;
    }

    const newData = { ...values };

    prevData.default_language = prevData.default_language ? prevData.default_language.code : null;
    prevData.supported_languages = prevData.supported_languages ? prevData.supported_languages.map((languages) => languages.code) : null;
    newData.starting_view ? newData.starting_view.split(' ')[0] : null;
    newData.experience_view = newData.experience_view ? newData.experience_view.sceneData._id : null;
    newData.default_language = newData.default_language ? newData.default_language.code : null;
    newData.supported_languages = newData.supported_languages ? newData.supported_languages.map((languages) => languages.code) : null;

    const payload = {}; // Object to store values to be sent
    payload.project_id = projectId.value;
    payload.query = {
      [projectSettingsFormTypes.ALE]: {},
    };

    const params = frameParams(prevData, newData);

    if (Object.keys(params).length > 0) {
      params.currency_support !== undefined ? payload.query[projectSettingsFormTypes.ALE].currency_support = params.currency_support : false;
      params.svg_visibility !== undefined ? payload.query[projectSettingsFormTypes.ALE].svg_visibility = params.svg_visibility : false;
      params.weblite_visibility !== undefined ? payload.query[projectSettingsFormTypes.ALE].is_enabled = params.weblite_visibility : false;
      params.share_scenes !== undefined ? payload.query[projectSettingsFormTypes.ALE].share_scenes = params.share_scenes : false;
      if (params.starting_view) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_type = params.starting_view;
      }
      if (params.experience_view) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_id = params.experience_view;
      }
      if (params.default_language) {
        payload.query[projectSettingsFormTypes.ALE].default_language = params.default_language;
      }

      if (params.supported_languages) {
        payload.query[projectSettingsFormTypes.ALE].supported_languages = [...params.supported_languages];
      }
    }

    if (Object.keys(params).length > 1) {
      saveSettings(payload);
    }
  });
};

if (!store.settings) {
  GetProjectById(projectId.value).then((res) => {
    store.settings = res;
    setupDataCallBack(res);
  });
} else {
  setupDataCallBack(store.settings);
}

</script>
<template>
    <div class="w-full h-full gap-4">

        <div class="flex justify-end items-center py-2 px-3 gap-4">
            <label for="editGeneralSettings"
                class="!text-[#1c64f2] bg-white border border-[#1c64f2] rounded-md flex flex-row justify-center items-center gap-[9px] px-5 py-3 h-10 m-0 cursor-pointer">
                Save
                <Spinner v-if="loader" />
            </label>

            <Button type="button" title="Publish" theme="primary" @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                        <path
                            d="M5.90065 3.24475L6.64345 2.50196C7.4798 1.6656 8.57475 1.31869 9.73355 1.262C10.1843 1.23995 10.4096 1.22892 10.5903 1.40964C10.7711 1.59036 10.76 1.81572 10.738 2.26645C10.6813 3.42525 10.3344 4.52021 9.49805 5.35655L8.75525 6.09935C8.14355 6.71105 7.96965 6.885 8.09805 7.5485C8.2248 8.05535 8.34745 8.54615 7.9789 8.9147C7.53185 9.36175 7.12405 9.36175 6.677 8.9147L3.08529 5.323C2.63824 4.87594 2.63823 4.46815 3.08529 4.0211C3.45383 3.65255 3.94464 3.77522 4.45148 3.90195C5.115 4.03037 5.28895 3.85645 5.90065 3.24475Z"
                            fill="white" />
                        <path d="M8.49805 3.5H8.50255" stroke="white" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M1.25 10.75L3.75 8.25" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <path d="M4.25 10.75L5.25 9.75" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <path d="M1.25 7.75L2.25 6.75" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                    </svg>
                </template>
            </Button>
        </div>

        <div class="w-full mb-1 px-3 py-1" v-if="initialData">
            <Form @submit="handleSubmit" :validation-schema="generalDesignSettingsSchema" :initial-values="initialData">

                <div class="grid grid-cols-2 gap-8 w-3/4 mt-3">
                    <!-- Weblite Visibility -->
                    <div class="flex justify-between">
                        <div class="flex flex-col gap-2">
                            <label for="weblite_visiblity" class="mb-0 text-sm font-semibold text-gray-900">
                                Weblite Visibility *</label>
                            <p class="text-gray-500 text-xs">If enabled, Weblite will be publicly visible to all</p>
                            <span class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="14" viewBox="0 0 16 14"
                                    class=" cursor-pointer">
                                    <g clip-path="url(#clip0_7536_31529)">
                                        <path
                                            d="M4.42083 14C3.67859 14.0011 2.95273 13.7917 2.33526 13.3984C1.71778 13.0051 1.2365 12.4455 0.952406 11.7907C0.668315 11.1359 0.594213 10.4153 0.739493 9.7202C0.884773 9.02512 1.24289 8.38688 1.76846 7.88638L4.75257 5.03748C5.45657 4.36672 6.41045 3.99001 7.40494 3.99001C8.39943 3.99001 9.3533 4.36672 10.0573 5.03748C10.199 5.18176 10.2763 5.373 10.273 5.57076C10.2696 5.76852 10.1858 5.95727 10.0393 6.09707C9.89279 6.23688 9.69507 6.31677 9.48798 6.31984C9.28089 6.32291 9.08067 6.24892 8.92969 6.11353C8.52489 5.72811 7.97657 5.51168 7.40494 5.51168C6.83331 5.51168 6.28499 5.72811 5.88019 6.11353L2.89607 8.96319C2.49168 9.34936 2.2645 9.87312 2.2645 10.4192C2.2645 10.6897 2.32028 10.9574 2.42864 11.2073C2.53701 11.4571 2.69584 11.6841 2.89607 11.8753C3.09631 12.0665 3.33402 12.2182 3.59564 12.3217C3.85725 12.4252 4.13765 12.4784 4.42083 12.4784C4.99272 12.4784 5.54119 12.2615 5.94558 11.8753L6.33235 11.506C6.40665 11.4353 6.49481 11.3792 6.59179 11.341C6.68877 11.3029 6.79267 11.2833 6.89757 11.2835C7.00246 11.2837 7.10629 11.3036 7.20313 11.3421C7.29997 11.3806 7.38792 11.4369 7.46196 11.5079C7.536 11.5788 7.59468 11.663 7.63465 11.7556C7.67462 11.8482 7.6951 11.9474 7.69491 12.0476C7.69473 12.1478 7.67388 12.2469 7.63357 12.3394C7.59326 12.4319 7.53427 12.5159 7.45997 12.5866L7.07319 12.9559C6.72488 13.2882 6.31126 13.5515 5.85608 13.7306C5.40091 13.9098 4.91315 14.0013 4.42083 14Z"
                                            fill="#3F83F8" />
                                        <path
                                            d="M8.60193 10.0111C8.10935 10.0118 7.62146 9.91961 7.16629 9.73979C6.71112 9.55996 6.29763 9.29605 5.94957 8.96319C5.8043 8.81957 5.72392 8.6272 5.72574 8.42753C5.72756 8.22786 5.81142 8.03686 5.95928 7.89566C6.10714 7.75447 6.30715 7.67438 6.51624 7.67264C6.72534 7.67091 6.92678 7.74766 7.07718 7.88638C7.48796 8.26085 8.03403 8.46979 8.60193 8.46979C9.16984 8.46979 9.71591 8.26085 10.1267 7.88638L13.11 5.03748C13.3106 4.84647 13.4698 4.61954 13.5784 4.36968C13.687 4.11982 13.7429 3.85195 13.7429 3.58143C13.7429 3.3109 13.687 3.04303 13.5784 2.79318C13.4698 2.54332 13.3106 2.31638 13.11 2.12537C12.6994 1.75078 12.1535 1.54174 11.5856 1.54174C11.0178 1.54174 10.4718 1.75078 10.0613 2.12537L9.46639 2.69348C9.31675 2.83627 9.11384 2.91645 8.9023 2.91638C8.69075 2.91631 8.4879 2.83599 8.33837 2.6931C8.18884 2.5502 8.10488 2.35643 8.10495 2.15442C8.10503 1.95241 8.18913 1.7587 8.33877 1.61591L8.93368 1.0478C9.63713 0.376548 10.591 -0.000356792 11.5855 2.53443e-07C12.5799 0.000357299 13.5335 0.377947 14.2364 1.0497C14.9393 1.72146 15.334 2.63236 15.3337 3.58201C15.3333 4.53166 14.9379 5.44228 14.2344 6.11353L11.2503 8.96243C10.9028 9.295 10.49 9.55882 10.0355 9.73876C9.58109 9.91871 9.09391 10.0112 8.60193 10.0111Z"
                                            fill="#3F83F8" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_7536_31529">
                                            <rect width="16" height="14" />
                                        </clipPath>
                                    </defs>
                                </svg>
                                <p class="text-blue-500 text-xs">{{ truncateString(copyString,30) }}</p>
                                <div class="w-8 cursor-pointer text-zinc-900 p-[4px]"
                                    @click="handleClipBoardCopy(copyString, 'copyMsg_View')">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"
                                        fill="none">
                                        <path
                                            d="M9.79187 0H7.875V3C7.875 3.6618 7.31437 4.2 6.625 4.2H3.5V8.4C3.5 9.0618 4.04187 9.6 4.70812 9.6H9.79187C10.4581 9.6 11 9.0618 11 8.4V1.2C11 0.5382 10.4581 0 9.79187 0Z"
                                            fill="#6B7280" />
                                        <path
                                            d="M6.625 3V0.0780001C6.32375 0.1608 6.04437 0.3084 5.81687 0.5274L4.04937 2.2242C3.82125 2.4426 3.6675 2.7108 3.58125 3H6.625Z"
                                            fill="#6B7280" />
                                        <path
                                            d="M8.5 10.2H7.25V10.8H2.25L2.24938 3.6H2.875V2.4H2.25C1.56062 2.4 1 2.9382 1 3.6V10.8C1 11.4618 1.54188 12 2.20813 12H7.27063C7.6125 12 7.93187 11.8698 8.16937 11.634C8.40687 11.3982 8.5 11.1312 8.5 10.7598C8.5 10.7454 8.5 10.2 8.5 10.2Z"
                                            fill="#6B7280" />
                                    </svg>
                                    <span id="copyMsg_View" class="copyMsgBox" style="visibility: hidden;">
                                        Copied
                                    </span>
                                </div>
                            </span>
                        </div>

                        <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                            <div class="relative mb-0 p-0">
                                <Field id="weblite_visibility" name="weblite_visibility" class="sr-only peer"
                                    type="checkbox" :value="true" />
                                <label for="weblite_visibility"
                                    class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                </label>
                            </div>
                            <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="weblite_visibility" />
                        </div>
                    </div>
                    <!-- Currency Support  -->
                    <div class="flex justify-between">
                        <div class="flex flex-col gap-2">
                            <label for="currency_support" class="mb-0 text-sm font-semibold text-gray-900">
                                Currency Support *</label>
                            <p class="text-gray-500 text-xs">If enabled, users can view prices in their local
                                currency
                            </p>
                        </div>
                        <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                            <div class="relative mb-0 p-0">
                                <Field id="currency_support" name="currency_support" class="sr-only peer"
                                    type="checkbox" :value="true" />
                                <label for="currency_support"
                                    class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                </label>
                            </div>
                            <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="currency_support" />
                        </div>
                    </div>

                    <!-- Default Language -->
                    <!-- Select One Min Language -->
                    <div class="flex">
                        <div class="flex  relative flex-col gap-2 w-3/4">
                            <label for="default_language" class="mb-0 text-sm font-semibold text-gray-900">
                                Select Default Language *</label>
                            <p class="text-gray-500 text-xs">This language will be used as the primary language for
                                experience.
                            </p>
                            <Field name="default_language" :model-value="default_language" v-slot="{ field }">
                                <multiselect :model-value="field.value" @update:model-value="val => field.onChange(val)"
                                    :options="languages" placeholder="Select Default Language" label="name"
                                    track-by="code" :multiple="false" maxHeight="250" />
                            </Field>
                            <ErrorMessage name="default_language" as="p" v-slot="{ message }">
                                <span class="text-xs font-normal text-red-600 capitalize "> {{ message }}</span>
                            </ErrorMessage>
                        </div>
                    </div>

                    <!-- Supported Language -->
                    <!-- Select One Min Language -->
                    <div class="flex">
                        <div class="flex flex-col gap-2 w-3/4">
                            <label for="supported_languages" class="mb-0 text-sm font-semibold text-gray-900">
                                Select Supported Languages *</label>
                            <p class="text-gray-500 text-xs">Add the languages your website will be available in.
                            </p>
                            <Field name="supported_languages" v-slot="{ field }">
                                <multiselect :model-value="field.value" @update:model-value="val => field.onChange(val)"
                                    :options="languages" placeholder="Select supported Language" label="name"
                                    track-by="code" :multiple="true" maxHeight="250" />
                            </Field>
                            <ErrorMessage name="supported_languages" as="p" v-slot="{ message }">
                                <span class="text-xs font-normal text-red-600 capitalize "> {{ message }}</span>
                            </ErrorMessage>
                        </div>
                    </div>

                    <!-- Share Scenes -->
                    <div class="flex justify-between">
                        <div class="flex flex-col gap-2">
                            <label for="share_scenes" class="mb-0 text-sm font-semibold text-gray-900">
                                Share Scenes *</label>
                            <p class="text-gray-500 text-xs">If enabled, users can share specific parts of the
                                project
                                scene
                                from the experience</p>
                        </div>
                        <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                            <div class="relative mb-0 p-0">
                                <Field id="share_scenes" name="share_scenes" class="sr-only peer" type="checkbox"
                                    :value="true" />
                                <label for="share_scenes"
                                    class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                </label>
                            </div>
                            <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="share_scenes" />
                        </div>
                    </div>

                    <!-- SVG Visibility -->
                    <div class="flex justify-between">
                        <div class="flex flex-col gap-2 mb-0">
                            <label for="svg_visibility" class="text-sm font-semibold text-gray-900">
                                SVG Visibility *
                            </label>
                            <p class="text-gray-500 text-xs">If enabled, a button will appear to show or hide the
                                SVG in
                                the experience.
                            </p>
                        </div>
                        <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                            <div class="relative mb-0 p-0">
                                <Field id="svg_visibility" name="svg_visibility" class="sr-only peer" type="checkbox"
                                    :value="true" />
                                <label for="svg_visibility"
                                    class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                </label>
                            </div>
                            <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="svg_visibility" />
                        </div>
                    </div>

                    <!-- Starting View -->
                    <div class="flex" v-if="initialSceneIdList !== null">
                        <div class="flex flex-col gap-2 w-3/4">
                            <label for="starting_view" class="mb-0 text-sm font-semibold text-gray-900">
                                Select Starting View Type *</label>
                            <p class="text-gray-500 text-xs">This will be project view type see when they open
                                experience.
                            </p>
                            <Field name="starting_view" v-slot="{ field }">
                                <multiselect :model-value="field.value" @update:model-value="val => field.onChange(val)"
                                    :options="sceneTypeList" placeholder="Select View Type" :multiple="false"
                                    :allow-empty="false" :searchable="false" :close-on-select="true"
                                    :show-labels="false" maxHeight="250" />
                            </Field>
                            <ErrorMessage name="starting_view" as="p" v-slot="{ message }">
                                <span class="text-xs font-normal text-red-600 capitalize "> {{ message }}</span>
                            </ErrorMessage>
                        </div>
                    </div>

                    <!-- Experience View -->
                    <div class="flex" v-if="initialSceneIdList !== null">
                        <div class="flex flex-col gap-2 w-3/4">
                            <label for="experience_view" class="mb-0 text-sm font-semibold text-gray-900">
                                Select Experience Starting View *</label>
                            <p class="text-gray-500 text-xs">This will be the first project view visitors see when they
                                open experience.</p>
                            <Field name="experience_view" :model-value="initialSceneIdRef" v-slot="{ field }">
                                <Multiselect :allow-empty="false" v-bind="field" v-model="initialSceneIdRef"
                                    :searchable="false" :close-on-select="true" :show-labels="false"
                                    :custom-label="(val) => val.sceneData.name" placeholder="Select View"
                                    :options="initialSceneIdList" maxHeight="250">
                                </Multiselect>
                            </Field>
                            <ErrorMessage name="experience_view" as="p" v-slot="{ message }">
                                <span class="text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                            </ErrorMessage>
                        </div>
                    </div>
                </div>
                <Button id="editGeneralSettings" class="hidden" title="Submit" type="submit" theme="primary">
                </Button>
            </Form>
        </div>

    </div>

</template>

<style scoped>

</style>
