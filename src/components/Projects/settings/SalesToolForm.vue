<script setup>
import { ref } from 'vue';
import { XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { SalesToolSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import Multiselect from 'vue-multiselect';
import TagModal from './TagModal.vue';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const SalestoolList = ['default', 'pixel_streaming', 'ale'];
const initialData = ref(null);
const default_experience = ref(null);
const projectTags = ref([]);
const showTagModal = ref(false);

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.salestool?.is_enabled ? data.projectSettings?.salestool?.is_enabled : false,
      default_experience: (data.projectSettings?.salestool?.default_experience ? data.projectSettings.salestool.default_experience : null),
      tags: data.projectSettings?.salestool?.tags ? data.projectSettings?.salestool?.tags : null,
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.salestool?.is_enabled ? data.projectSettings?.salestool?.is_enabled : false),
    };

    if (data.projectSettings?.salestool?.default_experience) {
      default_experience.value = (data.projectSettings.salestool.default_experience);
    }
    if (data.projectSettings?.salestool?.tags) {
      projectTags.value = [...data.projectSettings.salestool.tags];
    }

  }
};

const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values

    if (Object.keys(frameParms(prevData, newCompareObj)).length > 0) {
      const parms = frameParms(prevData, newCompareObj);

      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.SALESTOOL]: {},
        },
      };

      // Add all fields to the salestool object
      Object.keys(parms).forEach((key) => {
        payload.query.salestool[key] = parms[key];
      });

      UpdateProjectSettings(payload).then((res) => {
        if (res){
          projectStore.settings.projectSettings[projectSettingsFormTypes.SALESTOOL] = res.projectSettings[projectSettingsFormTypes.SALESTOOL]; // update to store
          setupDataCallBack(res); // update the values
          resolve(res);
        }
      });
    } else {
      resolve();
    }

  });
};

const openSlotModal = () => {
  showTagModal.value = true;
};

const handleTagModalConfirm = (tags) => {
  showTagModal.value = false;
  console.log("tags", tags);
  projectTags.value = tags;
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
  <div class="flex flex-col justify-start items-start my-3 ml-1">
    <!-- Headers -->
    <div class="flex flex-col w-full">
      <!-- Form -->
      <Form class="grid grid-cols-2 justify-start items-start w-[75%] gap-8" @submit="(val) => { val.tags = [...projectTags]; handleSubmit(val); }" :initial-values="initialData" :validation-schema="SalesToolSettingsSchema">
        <!-- is_enabled -->
        <div class="flex flex-col justify-start items-start">
          <div class="flex items-center w-full justify-between">
                  <div>
                    <div class="text-sm">Enable ALE</div>
                    <div class="text-xs text-gray-500">Some text here</div>
                  </div>
                  <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="sr-only peer"/>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:bg-blue-600 rounded-full peer transition-colors"></div>
                      <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
                    </label>
                  </Field>
          </div>
          <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- default_experience -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="default_experience">Initial Experience Type</label>
          <Field name="default_experience" :model-value="default_experience" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="default_experience" :searchable="false"
            :close-on-select="true" :show-labels="false" placeholder="Choose" :options="SalestoolList"
            maxHeight="250" class="!bg-[#F9FAFB]">
            </Multiselect>
          </Field>
          <ErrorMessage name="default_experience" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- tags -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black">Tags</label>
          <div class="border rounded-lg p-3 w-full">
            <Field name="tags" :model-value="projectTags" v-slot="{ field }">
              <div v-if="projectTags.length > 0" v-bind="field">
                <div class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                  <template v-if="projectTags.length > 0">
                    <span class="inline-flex items-center px-3 py-1 rounded bg-gray-100 text-gray-700 text-sm font-medium mr-2 mb-2" v-for="slots, index in projectTags" :key="index">
                      {{ slots }}
                      <XMarkIcon class="ml-2 w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer" @click="() => projectTags.splice(index,1)" />
                    </span>
                  </template>
                </div>
              </div>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button @click="openSlotModal" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                    <path d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z" fill="#1A56DB"/>
                  </svg>
                  Add Tag
                </button>
              </div>
            </Field>
          </div>
        </div>

        <Button id="editSalesToolSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
      </Form>
      <div class="flex justify-start items-center gap-3">
        <label for="editSalesToolSettings"
        :class="['bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] px-4 h-10 m-0 cursor-pointer']">
        Save <Spinner v-if="loader" /></label>
      </div>
    </div>
  </div>
  <TagModal
    v-if="showTagModal"
    v-model="showTagModal"
    :initialTags="projectTags"
    @confirm="handleTagModalConfirm"
  />
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}
</style>
