<script setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '../../../store/project.ts';
import GeneralForm from './GeneralForm.vue';
import PixelStreamForm from './PixelStreamForm.vue';
import HologramForm from './HologramForm.vue';

const route = useRoute();
const projectStore = ProjectStore();
const project_Id = ref(route.params.project_id); // Project id
const tabs = ['General', 'Pixel Streaming', 'Hologram'];
const activeTab = ref('General');
const generalFormRef = ref();
const pixelFormRef = ref();
const hologramFormRef = ref();

/* Methods */
onMounted(async () => {
  project_Id.value = route.params.project_id;
  if (project_Id.value){
    projectStore.settings = null;
    projectStore.RefreshSettings(route.params.project_id);
  }
});

function getCurrentFormRef () {
  if (activeTab.value === 'General') {
    return generalFormRef;
  }
  if (activeTab.value === 'Pixel Streaming') {
    return pixelFormRef;
  }
  if (activeTab.value === 'Hologram') {
    return hologramFormRef;
  }
  return null;
}

function handleTabChange (newTab) {
  const currentFormRef = getCurrentFormRef();
  if (currentFormRef.value?.isEdited) {
    if (!window.confirm('You have unsaved changes. Are you sure you want to switch tabs without saving?')) {
      return;
    }
  }
  activeTab.value = newTab;
}

</script>

<template>

  <div class="relative bg-transparent h-full">

    <!-- Header -->
    <div class="flex items-center justify-between mb-6 mt-2 w-full">
      <!-- Title -->
        <div class="dynamic-heading">
          <h3 class="dynamic-topic"> Project Settings </h3>
          <p class="dynamic-sub-topic"> Update Settings </p>
        </div>
    </div>
    <!-- Body -->
    <div v-if="projectStore.settings !== null" class="h-full">
      <!-- Tabs -->
      <div class="overflow-x-auto w-full">
        <div class="grid grid-cols-3 rounded-lg border border-gray-200 text-sm sm:w-[50vw]">
          <button
            type="button"
            v-for="(tab, index) in tabs"
            :key="tab"
            @click="handleTabChange(tab)"
            :class="[
              'py-3 px-4 text-center transition-colors duration-200',
              activeTab === tab ? 'bg-gray-100 text-blue-600 font-medium' : 'bg-white text-gray-500 hover:bg-gray-50',
              'border-l border-gray-200',
              index !== 0 ? 'border-l border-gray-200' : '',
              index === 0 ? 'rounded-l-lg' : '',
              index === tabs.length - 1 ? 'rounded-r-lg' : ''
            ]"
          >
            {{ tab }}
          </button>
        </div>
      </div>
      <!-- General Settings -->
      <GeneralForm v-if="activeTab === 'General'" ref="generalFormRef"/>

      <!-- Pixel Streaming Settings -->
      <PixelStreamForm v-if="activeTab === 'Pixel Streaming'" ref="pixelFormRef"/>

      <!-- Hologram -->
      <HologramForm v-if="activeTab === 'Hologram'" ref="hologramFormRef"/>

    </div>

  </div>
  <!-- <SlotModal
    v-if="showSlotModal"
    v-model="showSlotModal"
    :initialSlots="availableSlots"
    @confirm="onSlotsConfirm"
  /> -->

</template>

<style scoped>
.tab-buttons button.active {
  font-weight: bold;
  border-bottom: 2px solid #1A56DB;
}
</style>
