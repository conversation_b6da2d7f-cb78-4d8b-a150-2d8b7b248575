<script setup>
import { ref, watch } from 'vue';
import { UpdateProjectSettings, uploadSettingFiles } from '../../../api/projects/settings/index.ts';
import { AleSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import Multiselect from 'vue-multiselect';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ctaTypeList, languages } from '../../../helpers/constants.ts';
import { ProjectStore } from '../../../store/project.ts';
import { getAllScenes } from '../../../api/masterScene/index.ts';
import { getListofScenes } from '../../../api/projects/scene/index.ts';
import { ShortenUrl } from '../../../helpers/helpers.ts';
import { getCookie } from '../../../helpers/domhelper';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const shortenUrlRes = ref(null);
const org_url_domain = import.meta.env.VITE_UI_LIBRARY_STAG;
const sceneTypeList = ['project Scene', 'master Scene'];
const initialSceneTypeRef = ref(null);
const initialSceneIdRef = ref(null);
const intitialCtaType = ref(null);
const initialSceneIdList = ref(null);
const initial_default_language = ref(null);
const supported_languages = ref([]);
const loader = ref(false);
const aleVideoRef = ref({
  preview: null,
  fileData: null,
});
const aleThumbnailRef = ref({
  preview: null,
  fileData: null,
});

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

async function handleGenerate () {
  const current_organization = getCookie('organization');

  const org_url = `${org_url_domain}${current_organization}/projectscene/${project_Id.value}`; // org url
  const title = projectStore.settings?.name || ''; // name
  const description = projectStore.settings?.description || ''; // description
  const image = previousData.value?.thumbnail || ''; // thumbnail of ale

  const res = await ShortenUrl(org_url, title, description, image);
  if (res) {
    shortenUrlRes.value = res;
    isEdit.value = false;
  }
}

async function sceneIdApiCallBack (val) {

  return new Promise((resolve, reject) => {
    if (val.split(' ')[0] === 'master') {
      // Master Scene
      getAllScenes().then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
    } else {
      // Projects
      getListofScenes(project_Id.value).then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      });
    }
    initialSceneIdRef.value = null;
  });

}

watch(
  () => previousData.value,
  () => supported_languages.value,
  () => {
    handleGenerate();
  },
  { deep: true },
);

watch(initialSceneTypeRef, (val) => {
  sceneIdApiCallBack(val);  // Api Callback
});

const getLanguageByCode = (code) => {
  return languages.find((lang) => lang.code === code) || null;
};

const setupDataCallBack = (values) => {

  if (values) {

    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.is_enabled : false,
      currency_support: data.projectSettings?.ale?.currency_support ? data.projectSettings?.ale?.currency_support : false,
      is_cta_enabled: data.projectSettings?.ale?.is_cta_enabled ? data.projectSettings?.ale?.is_cta_enabled : false,
      initial_scene_type: (data.projectSettings?.ale?.initial_scene_type ? data.projectSettings.ale.initial_scene_type : null),
      initial_scene_id: (data.projectSettings?.ale?.initial_scene_id ? data.projectSettings?.ale?.initial_scene_id : null),
      cta_name: (data.projectSettings?.ale?.cta_name ? data.projectSettings?.ale?.cta_name : null),
      cta_type: (data.projectSettings?.ale?.cta_type ? data.projectSettings?.ale?.cta_type : null),
      thumbnail: (data.projectSettings?.ale?.welcome_thumbnail ? data.projectSettings?.ale?.welcome_thumbnail : null),
      initial_default_language: (data.projectSettings?.ale?.default_language ? getLanguageByCode(data.projectSettings?.ale?.default_language) : null ),
      supported_languages: (data.projectSettings?.ale?.supported_languages  ?  data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code))   : null ),
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.is_enabled : false),
      is_cta_enabled: (data.projectSettings?.ale?.is_cta_enabled ? data.projectSettings?.ale?.is_cta_enabled : false),
      cta_name: data.projectSettings?.ale?.cta_name,
      currency_support: (data.projectSettings?.ale.currency_support ? data.projectSettings?.ale.currency_support: false),
      supported_languages: (data.projectSettings?.ale?.supported_languages ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code)) : []),
    };

    if (data.projectSettings?.ale?.initial_scene_type) {
      initialSceneTypeRef.value = `${data.projectSettings.ale.initial_scene_type} Scene`;
    }

    if (data.projectSettings?.ale?.default_language) {
      initial_default_language.value = (data.projectSettings?.ale?.default_language ? getLanguageByCode(data.projectSettings?.ale?.default_language) : null);
    }

    if (data.projectSettings?.ale?.supported_languages) {
      supported_languages.value = (data.projectSettings?.ale?.supported_languages ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code))   : null);
    }
    if (data.projectSettings?.ale?.cta_type) {
      intitialCtaType.value = data.projectSettings?.ale?.cta_type;
    }

    if (data.projectSettings?.ale?.initial_scene_type) {
      sceneIdApiCallBack(`${data.projectSettings.ale.initial_scene_type} Scene`).then((result) => {
        initialSceneIdRef.value = result[data.projectSettings.ale.initial_scene_id];
      });
    }

    aleVideoRef.value.preview = data.projectSettings?.ale?.welcome_video;
    aleThumbnailRef.value.preview = data.projectSettings?.ale?.welcome_thumbnail;
  }
};
const saveSettings = async (payload) => {
  return UpdateProjectSettings(payload).then((result) => {
    aleVideoRef.value.fileData = null;
    aleThumbnailRef.value.fileData = null;
    if (result) {
      loader.value = false;
      isEdit.value = false;
      projectStore.settings.projectSettings[projectSettingsFormTypes.ALE] = result.projectSettings[projectSettingsFormTypes.ALE];
      setupDataCallBack(result);
      resolve(result);
    }
  });
};
const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    loader.value = true;
    const prevData = { ...previousData.value }; // prevData track source
    const newCompareObj = { ...val }; // form values

    // delete previous
    delete prevData.video;
    delete prevData.thumbnail;
    delete newCompareObj.video;
    delete newCompareObj.thumbnail;

    // object changes
    prevData.initial_default_language = prevData.initial_default_language ? prevData.initial_default_language.code : null;
    prevData.supported_languages = prevData.supported_languages ? prevData.supported_languages.map((languages) => languages.code) : null;
    newCompareObj.initial_scene_type = newCompareObj.initial_scene_type.split(' ')[0];
    newCompareObj.initial_scene_id = newCompareObj.initial_scene_id ? newCompareObj.initial_scene_id.sceneData._id : null;
    newCompareObj.initial_default_language = newCompareObj.initial_default_language ? newCompareObj.initial_default_language.code : null;
    newCompareObj.supported_languages = newCompareObj.supported_languages ? newCompareObj.supported_languages.map((languages) => languages.code) : null;

    const payload = {}; // Object to store values to be sent
    payload.project_id = project_Id.value;
    payload.query = {
      [projectSettingsFormTypes.ALE]: {},
    };

    const parms = frameParms(prevData, newCompareObj);

    if (Object.keys(parms).length > 0) {
      parms.is_enabled !== undefined ? payload.query[projectSettingsFormTypes.ALE].is_enabled = parms.is_enabled : '';
      parms.currency_support !== undefined ? payload.query[projectSettingsFormTypes.ALE].currency_support = parms.currency_support : '';
      if (parms.initial_scene_type) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_type = parms.initial_scene_type;
      }
      if (parms.initial_scene_id) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_id = parms.initial_scene_id;
      }
      if (parms.is_cta_enabled !== undefined) {
        payload.query[projectSettingsFormTypes.ALE].is_cta_enabled = parms.is_cta_enabled;
      }
      if (parms.cta_type) {
        payload.query[projectSettingsFormTypes.ALE].cta_type = parms.cta_type;
      }
      if (parms.cta_name) {
        payload.query[projectSettingsFormTypes.ALE].cta_name = parms.cta_name;
      }
      if (parms.initial_default_language) {
        payload.query[projectSettingsFormTypes.ALE].default_language = parms.initial_default_language;
      }

      if (parms.supported_languages) {
        payload.query[projectSettingsFormTypes.ALE].supported_languages = [...parms.supported_languages];
      }
    }

    if (Object.keys(parms).length > 0 || (val.video !== null && val.video !== undefined) || (val.thumbnail !== null && val.thumbnail !== undefined)) {

      const formData = new FormData();
      formData.append('project_id', project_Id.value);
      val.video && formData.append('welcome_video', val.video);
      val.thumbnail && formData.append('welcome_thumbnail', val.thumbnail);
      if (val.video || val.thumbnail){
        (async () => {
          const res  = await uploadSettingFiles(formData);
          if (res){
            if (res.welcome_thumbnail) {
              payload.query[projectSettingsFormTypes.ALE].welcome_thumbnail = res.welcome_thumbnail;
            }
            if (res.welcome_video) {
              payload.query[projectSettingsFormTypes.ALE].welcome_video = res.welcome_video;
            }
            await saveSettings(payload);
          }
        })(); // IIFE
      } else {
        saveSettings(payload);
      }
    } else {
      aleVideoRef.value.fileData = null;
      aleThumbnailRef.value.fileData = null;
      resolve();
    }
  });
};

// Initialize
if (projectStore.settings){
  console.log("Intialize Project Settings");
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
  <div class="flex flex-col justify-start items-start my-3 ml-1">
    <!-- Headers -->
    <div class="flex flex-col w-full">
      <!-- Form -->
      <Form class="grid grid-cols-2 justify-start items-start w-[75%] gap-8" @submit="(val) => handleSubmit(val)" :initial-values="initialData" :validation-schema="AleSettingsSchema">
        <!-- is_enabled -->
        <div class="flex flex-col justify-start items-start">
          <div class="flex items-center w-full justify-between">
                  <div>
                    <div class="text-sm">Enable ALE</div>
                    <div class="text-xs text-gray-500">Some text here</div>
                  </div>
                  <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="sr-only peer"/>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:bg-blue-600 rounded-full peer transition-colors"></div>
                      <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
                    </label>
                  </Field>
          </div>
          <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- currency_support -->
        <div class="flex flex-col justify-start items-start">
          <div class="flex items-center w-full justify-between">
                  <div>
                    <div class="text-sm">Currency Support</div>
                    <div class="text-xs text-gray-500">Some text here</div>
                  </div>
                  <Field v-slot="{ field }" name="currency_support" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" name="currency_support" v-bind="field" :value="true" class="sr-only peer"/>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:bg-blue-600 rounded-full peer transition-colors"></div>
                      <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
                    </label>
                  </Field>
          </div>
          <ErrorMessage name="currency_support" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- initial_scene_type -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="initial_scene_type">Initial Scene Type</label>
          <Field name="initial_scene_type" :model-value="initialSceneTypeRef" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="initialSceneTypeRef" :searchable="false"
            :close-on-select="true" :show-labels="false" placeholder="Choose" :options="sceneTypeList"
            maxHeight="250" class="!bg-[#F9FAFB]">
            </Multiselect>
          </Field>
          <ErrorMessage name="initial_scene_type" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- initial_scene_id -->
        <div class="flex flex-col justify-start items-start w-full" v-if="initialSceneIdList !== null">
          <label class="text-sm text-black" for="initial_scene_id">Initial Scene Id</label>
          <Field name="initial_scene_id" :model-value="initialSceneIdRef" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="initialSceneIdRef" :searchable="false"
            :close-on-select="true" :show-labels="false" :custom-label="(val) => val.sceneData.name" placeholder="Choose" :options="initialSceneIdList"
            maxHeight="250" class="!bg-[#F9FAFB]">
            </Multiselect>
          </Field>
          <ErrorMessage name="initial_scene_id" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- is_cta_enabled -->
        <div class="flex flex-col justify-start items-start">
          <div class="flex items-center w-full justify-between">
                  <div>
                    <div class="text-sm">Cta Enable</div>
                    <div class="text-xs text-gray-500">Some text here</div>
                  </div>
                  <Field v-slot="{ field }" name="is_cta_enabled" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" name="is_cta_enabled" v-bind="field" :value="true" class="sr-only peer"/>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:bg-blue-600 rounded-full peer transition-colors"></div>
                      <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
                    </label>
                  </Field>
          </div>
          <ErrorMessage name="is_cta_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- cta_type -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="cta_type">Cta Type</label>
          <Field name="cta_type" :model-value="intitialCtaType" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="intitialCtaType" :searchable="false"
            :close-on-select="true" :show-labels="false" placeholder="Choose" :options="ctaTypeList"
            maxHeight="250" class="!bg-[#F9FAFB]">
            </Multiselect>
          </Field>
          <ErrorMessage name="cta_type" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- cta_name -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="cta_name">Cta Name</label>
          <Field type="text" name="cta_name" id="cta_name" :required="autoScale" class="input-primary w-full bg-[#F9FAFB]" placeholder="Enter Cta Name" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="cta_name" />
        </div>
        <!-- initial_default_language -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="initial_default_language">Default Language</label>
          <Field name="initial_default_language" :model-value="initial_default_language" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="initial_default_language" :searchable="false" :close-on-select="true" :show-labels="false" placeholder="Choose" :options="languages" :custom-label="name" label="name" track-by="name" maxHeight="250" class="!bg-[#F9FAFB]">
            </Multiselect>
          </Field>
          <ErrorMessage name="initial_default_language" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- supported_languages -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black" for="supported_languages">Supported Language</label>
          <Field name="supported_languages" v-slot="{ field }">
            <Multiselect
              v-bind="field"
              :multiple="true"
              placeholder="Choose"
              :options="languages"
              label="name"
              track-by="code"
              maxHeight="250"
              class="!bg-[#F9FAFB]"
            />
          </Field>
          <ErrorMessage name="supported_languages" as="p" v-slot="{ message }"
                      class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                          fill="#B3261E" />
                      </svg>
                      <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- shorturl -->
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black mb-0" for="supported_languages">ShortUrl </label>
          <a v-if="shortenUrlRes" :href="shortenUrlRes" target="_blank" class="font-medium text-sm text-blue-500">
                {{ shortenUrlRes }}
                </a>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
          <div class="w-full flex flex-col gap-2">
            <button type="button" @click="handleGenerate()" class="bg-blue-500 text-white px-4 py-2 rounded-md mt-2">
              Generate Shorten Link
            </button>
          </div>
        </div>
        <Button id="editAleSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
      </Form>
      <div class="flex justify-start items-center gap-3 mb-4">
        <label for="editAleSettings"
        :class="['bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] px-4 h-10 m-0 cursor-pointer']">
        Save <Spinner v-if="loader" /></label>
      </div>
    </div>
  </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}
</style>
