<script setup>
import { ref } from 'vue';
import { XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { projectSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import moment from 'moment-timezone';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form, useForm } from 'vee-validate';
import { formatTimeTimezone } from '../../../helpers/domhelper';
import 'vue3-timepicker/dist/VueTimepicker.css';
import Multiselect from 'vue-multiselect';
import { ProjectStore } from '../../../store/project.ts';
import SlotModal from './SlotModal.vue';
import Spinner from '@/components/common/Spinner.vue';
import TagModal from './TagModal.vue';
import Modal from '@/components/common/Modal/Modal.vue';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref({ general: {}, pixelstreaming: {}, salestool: {}, metadata: {} }); // Previous data
const initialData = ref({ general: {}, pixelstreaming: {}, salestool: {}, metadata: {} });
const availableSlots = ref([]);
const timezones = moment.tz.names();
const selected_timezone = ref(null);
const showSlotModal = ref(false);
const showTagModal = ref(false);
const showMetaTagModal = ref(false);
const modalMetadata = ref([]);
const metadata = ref([]);
const tempSlots = ref([]);
const projectTags = ref([]);
const loader = ref(false);
const isEdited = ref(false);

const { resetForm, setFieldValue } = useForm();
const formKey = ref(0);
/* Methods */
const frameParms = (prev, curr) => {
  const diff = {};
  for (const key in curr) {
    const prevVal = prev[key];
    const currVal = curr[key];

    // Special case for metadata: always send full object if changed
    if (key === 'metadata') {
      if (JSON.stringify(prevVal) !== JSON.stringify(currVal)) {
        diff[key] = currVal; // send the whole metadata object
      }
    } else if (typeof currVal === 'object' && currVal !== null && !Array.isArray(currVal)) {
      const nestedDiff = frameParms(prevVal || {}, currVal);
      if (Object.keys(nestedDiff).length > 0) {
        diff[key] = nestedDiff;
      }
    } else if (Array.isArray(currVal)) {
      if (JSON.stringify(prevVal) !== JSON.stringify(currVal)) {
        diff[key] = currVal;
      }
    } else {
      if (prevVal !== currVal) {
        diff[key] = currVal;
      }
    }
  }
  for (const key in prev) {
    if (!(key in curr)) {
      diff[key] = null;
    }
  }
  return diff;
};

const trackChanges = () => {
  isEdited.value = true;
};

const setupDataCallBack = (values) => {
  if (values) {
    console.log("setupDataCallBack general");

    const data = values;

    // general
    previousData.value.general = {
      timezone: (data.projectSettings?.general?.timezone ? data.projectSettings.general.timezone : null),
      slots: (data.projectSettings?.general?.slots ? data.projectSettings.general.slots : null),
      lat: (data.projectSettings?.general?.lat ? data.projectSettings?.general.lat : ''),
      long: (data.projectSettings?.general?.long ? data.projectSettings?.general.long : ''),
    };
    initialData.value.general = {
      lat: (data.projectSettings?.general?.lat ? data.projectSettings?.general?.lat : ''),
      long: (data.projectSettings?.general?.long ? data.projectSettings?.general?.long : ''),
    };

    if (data.projectSettings?.general?.slots) {
      availableSlots.value = [...data.projectSettings.general.slots];
    }

    if (data.projectSettings?.general?.timezone) {
      selected_timezone.value = (data.projectSettings.general.timezone);
    }

    // pixelstreaming
    previousData.value.pixelstreaming = {
      session_duration: (data.projectSettings?.pixelstreaming?.session_duration ? data.projectSettings?.pixelstreaming?.session_duration : null),
    };
    initialData.value.pixelstreaming = {
      session_duration: data.projectSettings?.pixelstreaming?.session_duration,
    };

    // salestool
    previousData.value.salestool = {
      tags: data.projectSettings?.salestool?.tags ? data.projectSettings?.salestool?.tags : null,
    };
    initialData.value.salestool = {
      tags: data.projectSettings?.salestool?.tags ? data.projectSettings?.salestool?.tags : [],
    };
    if (data.projectSettings?.salestool?.tags) {
      projectTags.value = [...data.projectSettings.salestool.tags];
    }

    // metadata
    console.log("data.projectSettings?.metadata", data.projectSettings);
    previousData.value.metadata = data.projectSettings?.metadata ? { ...data.projectSettings.metadata } : {};
    if (data.projectSettings?.metadata) {
      metadata.value = Object.entries(data.projectSettings.metadata).map(([key, value]) => ({ key, value }));
    }

  }
};

const handleSubmit = async (val) => {
  loader.value = true;
  try {
    return await new Promise((resolve) => {
      const metadataObj = {};
      metadata.value.forEach((elem) => {
        if (elem.key && elem.value) {
          metadataObj[elem.key] = elem.value;
        }
      });
      const prevData = previousData.value;
      const newCompareObj = { general: { ...val.general }, pixelstreaming: { ...val.pixelstreaming }, salestool: { ...val.salestool }, metadata: { ...metadataObj } };

      const formData = new FormData();
      formData.append('project_id', project_Id.value);

      const payload = {
        project_id: project_Id.value,
        query: frameParms(prevData, newCompareObj),
      };

      UpdateProjectSettings(payload).then((result) => {
        if (result) {
          console.log("projectStore.settings.projectSettings", projectStore.settings, result);
          projectStore.settings.projectSettings = result.projectSettings; // update to store
          setupDataCallBack(result); // update the values
          resolve(result);
        }
      });
    });
  } finally {
    loader.value = false;
    isEdited.value = false;
  }
};

const openSlotModal = () => {
  // Pass a copy to avoid direct mutation before confirm
  tempSlots.value = availableSlots.value.map((slot) => slot);
  showSlotModal.value = true;
};

const openTagModal = () => {
  showTagModal.value = true;
};

const handleTagModalConfirm = (tags) => {
  showTagModal.value = false;
  projectTags.value = tags;
  trackChanges();
};

const handleSlotModalConfirm = (slots) => {
  console.log('slots', slots);
  const timezone = selected_timezone.value || 'Asia/Kolkata'; // fallback if not set
  const targetDate = '2025-06-30'; // or dynamically chosen date if needed

  const isoSlots = slots.map((slot) => {
    let hour = parseInt(slot.hh, 10);
    const minute = parseInt(slot.mm, 10);

    if (slot.ampm === 'PM' && hour !== 12) {
      hour += 12;
    }
    if (slot.ampm === 'AM' && hour === 12) {
      hour = 0;
    }

    const datetime = moment.tz(`${targetDate} ${hour}:${minute}`, 'YYYY-MM-DD H:mm', timezone);
    return datetime.toISOString(); // UTC ISO format
  });

  console.log('Converted slots:', isoSlots);
  availableSlots.value = isoSlots;
  trackChanges();
  // If slots are objects, convert to ISO strings
  // availableSlots.value = slots.map(slot => {
  //   // If already a string, return as is
  //   if (typeof slot === 'string') return slot;
  //   // If object, convert to ISO string (adjust as per your slot object structure)
  //   if (slot && slot.time) {
  //     // Example: slot.time = "10:00", slot.period = "AM", slot.timezone = "Asia/Kolkata"
  //     const [hour, minute] = slot.time.split(':');
  //     let hours24 = parseInt(hour, 10);
  //     if (slot.period === 'PM' && hours24 !== 12) hours24 += 12;
  //     if (slot.period === 'AM' && hours24 === 12) hours24 = 0;
  //     const now = moment.tz(slot.timezone || selected_timezone.value);
  //     now.set({ hour: hours24, minute: parseInt(minute, 10), second: 0, millisecond: 0 });
  //     return now.toISOString();
  //   }
  //   return slot;
  // });
};

const openAddMetadataModal = () => {
  // Copy current metadata to modal state
  modalMetadata.value = metadata.value.length
    ? metadata.value.map((item) => ({ ...item }))
    : [{ key: '', value: '' }];
  showMetaTagModal.value = true;
};

const addModalPair = () => {
  modalMetadata.value.push({ key: '', value: '' });
};

const removeModalPair = (index) => {
  modalMetadata.value.splice(index, 1);
};

const handleModalMetadataChange = (e, ind) => {
  modalMetadata.value[ind][e.target.name] = e.target.value;
};

const confirmModalMetadata = () => {
  metadata.value = modalMetadata.value.filter(
    (item) => item.key && item.value,
  );
  showMetaTagModal.value = false;
  trackChanges();
};

const handleCancel = () => {
  const newInitialData = {
    general: {
      lat: previousData.value.general.lat || '',
      long: previousData.value.general.long || '',
    },
    pixelstreaming: {
      session_duration: previousData.value.pixelstreaming.session_duration || '',
    },
    salestool: {
      tags: previousData.value.salestool.tags ? [...previousData.value.salestool.tags] : [],
    },
    metadata: previousData.value.metadata ? { ...previousData.value.metadata } : {},
  };
  initialData.value = newInitialData;
  resetForm({ values: newInitialData });
  formKey.value++;
  setFieldValue('general.lat', newInitialData.general.lat);
  setFieldValue('general.long', newInitialData.general.long);
  setFieldValue('pixelstreaming.session_duration', newInitialData.pixelstreaming.session_duration);

  selected_timezone.value = previousData.value.general.timezone;
  projectTags.value = previousData.value.salestool.tags ? [...previousData.value.salestool.tags] : [];
  availableSlots.value = previousData.value.general.slots ? [...previousData.value.general.slots] : [];
  metadata.value = previousData.value.metadata
    ? Object.entries(previousData.value.metadata).map(([key, value]) => ({ key, value }))
    : [];
  isEdited.value = false;
};

// Initialize
if (projectStore.settings){
  console.log("I'm from General settings");
  setupDataCallBack(projectStore.settings);
}

defineExpose({
  isEdited,
  handleCancel,
});

</script>

<template>
  <!-- General Settings -->
  <div class="sm:w-[calc(100%-240px)] relative mt-3">
    <!-- Headers -->
    <div class="flex flex-col sm:w-[50vw] mb-4 h-full">
      <Form
        :key="formKey"
        class="grid grid-cols-2 justify-start items-start sm:w-[50vw] gap-8"
        @submit="(val) => { console.log('val', val); val.salestool.tags = [...projectTags]; handleSubmit(val) }"
        :initial-values="initialData" :validation-schema="projectSettingsSchema" v-slot="{ values }">
        {{ console.log('values', values) }}
        <!-- timezone -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Select a Time zone*</span>
            <span class="text-xs text-gray-500">Meeting will be scheduled based on your selected time zone</span>
          </div>
          <Field name="general.timezone" :model-value="selected_timezone" v-slot="{ field }">
            <Multiselect :allow-empty="false" v-bind="field" v-model="selected_timezone" :searchable="false"
              :close-on-select="true" :show-labels="false" placeholder="Choose" :options="timezones" maxHeight="250"
              class="!bg-[#F9FAFB]" @update:modelValue="trackChanges">
            </Multiselect>
          </Field>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="general.timezone" />
        </div>
        <!-- session_duration -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Meeting Duration*</span>
            <span class="text-xs text-gray-500">Select how long each Meetings remains active.</span>
          </div>
          <Field type="number" name="pixelstreaming.session_duration" id="pixelstreaming.session_duration"
            class="input-primary w-full bg-gray-50" placeholder="Enter Session Duration" @input="trackChanges"/>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="pixelstreaming.session_duration" />
        </div>
        <!-- tags -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Add Meeting Tag*</span>
            <span class="text-xs text-gray-500">This tag to label and organize meetings when scheduling with
              clients.</span>
          </div>
          <div class="border rounded-lg p-3 w-full flex gap-2 flex-col">
            <Field name="salestool.tags" :model-value="projectTags" v-slot="{ field }">
              <div v-if="projectTags.length > 0" v-bind="field">
                <div
                  class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                  <template v-if="projectTags.length > 0">
                    <span
                      class="inline-flex items-center px-3 py-1 rounded bg-gray-100 text-gray-700 text-sm font-medium mr-2 mb-2"
                      v-for="slots, index in projectTags" :key="index">
                      {{ slots }}
                      <XMarkIcon class="ml-2 w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer"
                        @click="() => { projectTags.splice(index, 1); trackChanges() }" />
                    </span>
                  </template>
                </div>
              </div>
              <span v-if="!projectTags.length" class="text-xs text-gray-500">No tag is added</span>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button type="button" @click="openTagModal" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                    <path
                      d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z"
                      fill="#1A56DB" />
                  </svg>
                  Add Tag
                </button>
              </div>
            </Field>
          </div>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="salestool.tags" />
        </div>
        <!-- slot -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Add Time Solt*</span>
            <span class="text-xs text-gray-500">This tag to label and organize meetings when scheduling with
              clients.</span>
          </div>
          <div class="border rounded-lg p-3 w-full flex gap-2 flex-col">
            <Field name="general.slots" :model-value="availableSlots" v-slot="{ field }">
              <div v-if="availableSlots.length > 0" v-bind="field">
                <div
                  class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                  <template v-if="availableSlots.length > 0">
                    <span
                      class="inline-flex items-center px-3 py-1 rounded bg-gray-100 text-gray-700 text-sm font-medium mr-2 mb-2"
                      v-for="(slot, index) in availableSlots" :key="index">
                      {{ formatTimeTimezone(slot, selected_timezone, 'h:mma') }}
                      <XMarkIcon class="ml-2 w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer"
                        @click="() => { availableSlots.splice(index, 1); trackChanges() }" />
                    </span>
                  </template>
                </div>
              </div>
              <span v-if="!availableSlots.length" class="text-xs text-gray-500">No tag is added</span>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button type="button" @click="openSlotModal" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                      <path
                        d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z"
                        fill="#1A56DB" />
                  </svg>
                  Add Slot
                </button>
              </div>
            </Field>
          </div>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="general.slots" />
        </div>
        <!-- metadata -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Add Integration Metadata*</span>
            <span class="text-xs text-gray-500">Enter values for the metadata keys provided by the client. These key–value pairs help connect with client system</span>
          </div>
          <div class="border rounded-lg p-3 w-full flex gap-2 flex-col">
            <div class="flex flex-col gap-2" v-if="metadata.length > 0">
              <div v-for="(elem, index) in metadata" :key="index" class="flex gap-2 items-center text-sm bg-gray-200 text-gray-600 w-fit px-2 py-1 rounded-[4px]">
                {{ elem.key }} - {{ elem.value }}
                <svg class="cursor-pointer" @click="openAddMetadataModal" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                  <g clip-path="url(#clip0_7536_30816)">
                    <path d="M6.91804 3.78807L6.91553 3.79157L3.34686 7.3606L4.41641 8.42976L7.98809 4.85823L6.91804 3.78807Z" fill="#6B7280"/>
                    <path d="M1.56977 5.58334L2.63932 6.6535L6.2085 3.08446L6.212 3.08196L5.14145 2.0113L1.56977 5.58334Z" fill="#6B7280"/>
                    <path d="M0.997742 6.42546L0.025694 9.3414C-0.0343089 9.52093 0.0126933 9.71946 0.1467 9.85298C0.241705 9.94849 0.369711 10 0.500217 10C0.55322 10 0.606723 9.9915 0.658225 9.974L3.57387 9.00185L0.997742 6.42546Z" fill="#6B7280"/>
                    <path d="M9.41116 0.588088C8.62662 -0.196029 7.34956 -0.196029 6.56502 0.588088L5.84898 1.3042L8.69562 4.15112L9.41166 3.43502C10.1962 2.6504 10.1962 1.37271 9.41116 0.588088Z" fill="#6B7280"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_7536_30816">
                      <rect width="10" height="10" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
                <XMarkIcon class="w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer" @click="() => { metadata.splice(index, 1); trackChanges() }" />
              </div>
            </div>
            <span v-if="!metadata.length" class="text-xs text-gray-500">No tag is added</span>
            <div class="flex gap-2 text-sm items-center justify-stretch my-2">
              <button type="button" @click="openAddMetadataModal" class="flex items-center gap-2 text-blue-700">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                  <path
                    d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z"
                    fill="#1A56DB" />
                </svg>
                Add Metadata
              </button>
            </div>
          </div>
        </div>
        <!-- Latitude, Longitude -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Project Location Coordinates*</span>
            <span class="text-xs text-gray-500">This will be used to mark the exact location of the project</span>
          </div>
          <div class="flex gap-6 w-full">
            <div class="flex flex-col w-full gap-1">
              <Field type="number" name="general.lat" id="general.lat" class="input-primary w-full bg-[#F9FAFB]" placeholder="Enter Latitude" @input="trackChanges"/>
              <ErrorMessage as="p" class="text-sm text-rose-500" name="general.lat" />
            </div>
            <div class="flex flex-col w-full gap-1">
              <Field type="number" name="general.long" id="general.long" class="input-primary w-full bg-[#F9FAFB]" placeholder="Enter Longitude" @input="trackChanges"/>
              <ErrorMessage as="p" class="text-sm text-rose-500" name="general.long" />
            </div>
          </div>
        </div>
        <Button id="editGeneralSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
      </Form>
      <div v-if="isEdited"
        class="fixed bottom-0 left-0 w-full sm:left-[240px] sm:w-[calc(100%-240px)] z-50 bg-white dark:bg-bg-1000
              flex justify-end sm:justify-center items-center gap-3 px-8 py-4">
        <label for="editGeneralSettings"
          class="bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150
                rounded-lg flex flex-row justify-center items-center gap-[9px]
                px-4 h-10 m-0 cursor-pointer">
          Save
          <Spinner v-if="loader" />
        </label>
        <button type="reset"
          class="bg-gray-100 dark:bg-bg-1000 text-gray-500 dark:text-txt-150
                rounded-lg flex flex-row justify-center items-center gap-[9px]
                px-4 h-10 m-0 cursor-pointer"
          @click="handleCancel">
          Cancel
        </button>
      </div>
    </div>
  </div>
  <!-- Slot Modal -->
  <SlotModal v-if="showSlotModal" v-model="showSlotModal" :initialSlots="tempSlots" @confirm="handleSlotModalConfirm" />
  <TagModal v-if="showTagModal" v-model="showTagModal" :type="'tag'" :initialTags="projectTags" @confirm="handleTagModalConfirm" />
  <Modal :open="showMetaTagModal" @update:modelValue="showMetaTagModal = $event">
    <div class="modal-backdrop">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="font-bold text-lg">Add Metadata</h3>
          <button class="close-btn" @click="showMetaTagModal = false">×</button>
        </div>
        <div class="modal-body p-0">
          <div class="flex flex-col gap-1 mt-[6px]">
            <div class="flex text-gray-500 text-sm">
              <span class="w-1/2">Key</span>
              <span class="w-1/2">Value</span>
            </div>
            <div v-for="(pair, idx) in modalMetadata" :key="idx" class="flex items-center gap-2 mb-2">
              <input class="border rounded p-2 text-sm w-1/2" placeholder="Key" name="key" :value="pair.key"
                @input="e => handleModalMetadataChange(e, idx)" />
              <input class="border rounded p-2 text-sm w-1/2" placeholder="Value" name="value" :value="pair.value"
                @input="e => handleModalMetadataChange(e, idx)" />
              <button @click="removeModalPair(idx)" v-if="modalMetadata.length > 1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                  <g clip-path="url(#clip0_7502_34218)">
                    <path
                      d="M6.37109 1H9.62988C9.92401 1 10.2097 1.12055 10.4229 1.34082C10.6366 1.56173 10.7598 1.86528 10.7598 2.18457V4.36816H14.5186C14.5966 4.36816 14.675 4.40052 14.7354 4.46289C14.7963 4.52588 14.834 4.61499 14.834 4.71094C14.8339 4.80673 14.7962 4.89511 14.7354 4.95801C14.6749 5.02043 14.5966 5.05273 14.5186 5.05273H13.2041V14.8154C13.2041 15.1347 13.0809 15.4383 12.8672 15.6592C12.654 15.8794 12.3683 16 12.0742 16H3.92578C3.63177 15.9999 3.34591 15.8794 3.13281 15.6592C2.91914 15.4383 2.79688 15.1347 2.79688 14.8154V5.05273H1.48145C1.40349 5.05264 1.32496 5.02034 1.26465 4.95801C1.20397 4.89513 1.1671 4.80657 1.16699 4.71094C1.16699 4.61511 1.20384 4.52586 1.26465 4.46289C1.32496 4.40056 1.40349 4.36826 1.48145 4.36816H5.24121V2.18457C5.24121 1.86528 5.36437 1.56173 5.57812 1.34082C5.7646 1.14823 6.00628 1.03198 6.26074 1.00586L6.37109 1ZM6.37109 5.89453C6.01699 5.89453 5.68075 6.04055 5.43555 6.29395C5.19088 6.54681 5.05566 6.88657 5.05566 7.2373V13.1318C5.05573 13.4825 5.19094 13.8224 5.43555 14.0752C5.68072 14.3284 6.01716 14.4736 6.37109 14.4736C6.72507 14.4735 7.06152 14.3285 7.30664 14.0752C7.55125 13.8224 7.68548 13.4825 7.68555 13.1318V7.2373C7.68555 6.88674 7.55109 6.54677 7.30664 6.29395C7.06152 6.04062 6.72507 5.89464 6.37109 5.89453ZM9.62988 5.89453C9.27578 5.89455 8.93953 6.04054 8.69434 6.29395C8.44979 6.54679 8.31543 6.88666 8.31543 7.2373V13.1318C8.3155 13.4825 8.44973 13.8224 8.69434 14.0752C8.93951 14.3285 9.27587 14.4736 9.62988 14.4736C9.98389 14.4736 10.3202 14.3285 10.5654 14.0752C10.81 13.8224 10.9443 13.4825 10.9443 13.1318V7.2373C10.9443 6.88663 10.81 6.5468 10.5654 6.29395C10.3202 6.04052 9.98401 5.89453 9.62988 5.89453ZM5.87109 4.36816H10.1299V1.68457H5.87109V4.36816Z"
                      fill="#F05252" stroke="#F05252" />
                  </g>
                  <defs>
                    <clipPath id="clip0_7502_34218">
                      <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </button>
              <button @click="addModalPair" v-if="idx === modalMetadata.length - 1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                  <path
                    d="M7.99902 1C9.98759 1.0023 11.8946 1.79309 13.3008 3.19922C14.6191 4.5175 15.3963 6.27573 15.4902 8.12891L15.5 8.50098C15.4998 9.984 15.0603 11.4339 14.2363 12.667C13.4122 13.9003 12.2405 14.861 10.8701 15.4287C9.49973 15.9963 7.99191 16.1448 6.53711 15.8555C5.08225 15.5661 3.74518 14.8526 2.69629 13.8037C1.6474 12.7548 0.93392 11.4177 0.644531 9.96289C0.355194 8.50809 0.503657 7.00027 1.07129 5.62988C1.63895 4.2595 2.59968 3.08776 3.83301 2.26367C5.0661 1.43974 6.516 1.00019 7.99902 1ZM8 3.58594C7.64406 3.58594 7.30247 3.72684 7.05078 3.97852C6.7991 4.2302 6.65822 4.57181 6.6582 4.92773V7.1582H4.42773C4.07181 7.15822 3.7302 7.2991 3.47852 7.55078C3.22684 7.80247 3.08594 8.14407 3.08594 8.5C3.08594 8.85594 3.22684 9.19753 3.47852 9.44922C3.73019 9.7009 4.07181 9.84178 4.42773 9.8418H6.6582V12.0723C6.65822 12.4282 6.7991 12.7698 7.05078 13.0215C7.30247 13.2732 7.64406 13.4141 8 13.4141C8.35594 13.4141 8.69753 13.2732 8.94922 13.0215C9.2009 12.7698 9.34178 12.4282 9.3418 12.0723V9.8418H11.5723C11.9282 9.84178 12.2698 9.7009 12.5215 9.44922C12.7732 9.19753 12.9141 8.85594 12.9141 8.5C12.9141 8.14406 12.7732 7.80247 12.5215 7.55078C12.2698 7.2991 11.9282 7.15822 11.5723 7.1582H9.3418V4.92773C9.34178 4.5718 9.2009 4.2302 8.94922 3.97852C8.69753 3.72684 8.35594 3.58594 8 3.58594Z"
                    fill="#1A56DB" stroke="#1A56DB" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <button class="confirm-btn" @click="confirmModalMetadata">Confirm</button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
  background-color: #F9FAFB;
}

/* Time Picker */
.vue__time-picker input.vue__time-picker-input {
  background-color: transparent;
  text-align: left;
  color: #323232;
  border-radius: 0.375rem;
}

.vue__time-picker input.vue__time-picker-input::placeholder {
  text-align: left;
}

.vue__time-picker .clear-btn {
  color: red;
}

.vue__time-picker .vue__time-picker-input {
  height: 43px !important;
}

.modal-backdrop {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  width: fit-content;
  min-height: fit-content;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  max-height: 509px;
}

.modal-body {
  flex: 1 1 auto;
  overflow-y: auto;
  max-height: 350px;
  padding: 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0;
  padding: 0;
  padding-bottom: 6px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.input-time {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 4px 8px;
  width: 135px;
  appearance: auto;
}

.add-btn {
  color: #fff;
  border: none;
  border-radius: 20px;
  width: 28px;
  height: 28px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: 0;
  margin-right: 4px;
}

.confirm-btn {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 24px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
}

.modal-footer {
  border-top: 0;
  padding-top: 16px;
}

.slot-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 80px;
  gap: 8px 8px;
  align-items: center;
}

.slot-grid-header {
  display: contents;
}

.slot-grid-row {
  display: contents;
}
</style>
