<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import { UpdateProjectSettings } from '@/api/projects/settings';
import Multiselect from 'vue-multiselect';
import { CardDetailsSchema } from '../../../validationSchema/project/settings';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const initialData = ref(null);
const selected_unitCard = ref();
const cardCustomizationTypes = ['default', 'custom'];
const project_Id = ref(route.params.project_id); // Project id

const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const setupDataCallBack = (values) => {
  console.log('setupDataCallBack', values);

  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      customize_type: data.projectSettings?.ale?.unit_card_customize_type ? data.projectSettings?.ale?.unit_card_customize_type : 'default',
      type: data.projectSettings?.ale?.unitcard_config?.type ? data.projectSettings?.ale?.unitcard_config?.type : false,
      measurement: data.projectSettings?.ale?.unitcard_config?.measurement ? data.projectSettings?.ale?.unitcard_config?.measurement : false,
      bedrooms: data.projectSettings?.ale?.unitcard_config?.bedrooms ? data.projectSettings?.ale?.unitcard_config?.bedrooms : false,
      bathrooms: data.projectSettings?.ale?.unitcard_config?.bathrooms ? data.projectSettings?.ale?.unitcard_config?.bathrooms : false,
      status: data.projectSettings?.ale?.unitcard_config?.status ? data.projectSettings?.ale?.unitcard_config?.status : false,
      price: data.projectSettings?.ale?.unitcard_config?.price ? data.projectSettings?.ale?.unitcard_config?.price : false,
      maid: data.projectSettings?.ale?.unitcard_config?.maid ? data.projectSettings?.ale?.unitcard_config?.maid : false,
      view: data.projectSettings?.ale?.unitcard_config?.view ? data.projectSettings?.ale?.unitcard_config?.view : false,
      floor_id: data.projectSettings?.ale?.unitcard_config?.floor_id ? data.projectSettings?.ale?.unitcard_config?.floor_id : false,
      building_id: data.projectSettings?.ale?.unitcard_config?.building_id ? data.projectSettings?.ale?.unitcard_config?.building_id : false,
      style: data.projectSettings?.ale?.unitcard_config?.style ? data.projectSettings?.ale?.unitcard_config?.style : false,
      units: data.projectSettings?.ale?.unitcard_config?.units ? data.projectSettings?.ale?.unitcard_config?.units : false,
      favIcon: data.projectSettings?.ale?.unitcard_config?.favIcon ? data.projectSettings?.ale?.unitcard_config?.favIcon : false,
    };

    // Form Initial Values
    initialData.value = {
      type: data.projectSettings?.ale?.unitcard_config?.type ? data.projectSettings?.ale?.unitcard_config?.type : false,
      measurement: data.projectSettings?.ale?.unitcard_config?.measurement ? data.projectSettings?.ale?.unitcard_config?.measurement : false,
      bedrooms: data.projectSettings?.ale?.unitcard_config?.bedrooms ? data.projectSettings?.ale?.unitcard_config?.bedrooms : false,
      bathrooms: data.projectSettings?.ale?.unitcard_config?.bathrooms ? data.projectSettings?.ale?.unitcard_config?.bathrooms : false,
      status: data.projectSettings?.ale?.unitcard_config?.status ? data.projectSettings?.ale?.unitcard_config?.status : false,
      price: data.projectSettings?.ale?.unitcard_config?.price ? data.projectSettings?.ale?.unitcard_config?.price : false,
      maid: data.projectSettings?.ale?.unitcard_config?.maid ? data.projectSettings?.ale?.unitcard_config?.maid : false,
      view: data.projectSettings?.ale?.unitcard_config?.view ? data.projectSettings?.ale?.unitcard_config?.view : false,
      floor_id: data.projectSettings?.ale?.unitcard_config?.floor_id ? data.projectSettings?.ale?.unitcard_config?.floor_id : false,
      building_id: data.projectSettings?.ale?.unitcard_config?.building_id ? data.projectSettings?.ale?.unitcard_config?.building_id : false,
      style: data.projectSettings?.ale?.unitcard_config?.style ? data.projectSettings?.ale?.unitcard_config?.style : false,
      units: data.projectSettings?.ale?.unitcard_config?.units ? data.projectSettings?.ale?.unitcard_config?.units : false,
      favIcon: data.projectSettings?.ale?.unitcard_config?.favIcon ? data.projectSettings?.ale?.unitcard_config?.favIcon : false,
    };

    selected_unitCard.value = data.projectSettings?.ale?.unit_card_customize_type ? data.projectSettings?.ale?.unit_card_customize_type : 'default';

  }
};
const handleSubmit = async (val) => {
  console.log('val', val);

  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values
    console.log('newCompareObj', newCompareObj);

    if (Object.keys(frameParms(prevData, newCompareObj)).length > 0) {
      const oldValues = Object.fromEntries(
        Object.entries(prevData).filter(([key]) => key !== 'customize_type'),
      );
      const newValues = Object.fromEntries(
        Object.entries(newCompareObj).filter(([key]) => key !== 'customize_type'),
      );

      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.ALE]: {
            unitcard_config: frameParms(oldValues, newValues),
          },
        },
      };

      const params = frameParms(prevData, newCompareObj);
      if (params.customize_type){
        payload.query[projectSettingsFormTypes.ALE].unit_card_customize_type = params.customize_type;
      } else {
        payload.query[projectSettingsFormTypes.ALE].unit_card_customize_type = previousData.value.customize_type;
      }

      UpdateProjectSettings(payload).then((res) => {
        if (res){
          projectStore.settings.projectSettings[projectSettingsFormTypes.UNITCARDREQUIREMENTS] = res.projectSettings[projectSettingsFormTypes.UNITCARDREQUIREMENTS]; // update to store
          setupDataCallBack(res); // update the values
          resolve(res);
        }
      });
    } else {
      resolve();
    }
  });
};

if (projectStore.settings){
  console.log('projectStore.settings', projectStore.settings);
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
    <div class="flex flex-col justify-start items-start my-3 ml-1">
        <!-- Headers -->
        <div class="flex flex-col w-full">
            <!-- Form -->
            <Form class="flex flex-col justify-start items-start w-[36%] gap-8" @submit="(val) => handleSubmit(val)" :initial-values="initialData" :validation-schema="CardDetailsSchema">
                <!-- customize_type -->
                <div class="flex flex-col justify-start items-start w-full">
                    <label class="text-sm text-black" for="customize_type">Unit Card Customization</label>
                    <Field name="customize_type" :model-value="selected_unitCard" v-slot="{ field }">
                        <Multiselect :allow-empty="false" v-bind="field" v-model="selected_unitCard" :searchable="false"
                        :close-on-select="true" :show-labels="false" placeholder="Choose" :options="cardCustomizationTypes"
                        maxHeight="250" class="!bg-[#F9FAFB]">
                        </Multiselect>
                    </Field>
                    <ErrorMessage name="customize_type" as="p" v-slot="{ message }"
                                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                    fill="#B3261E" />
                                </svg>
                                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>
                </div>
                <div class="grid grid-cols-2 gap-4 w-full">
                    <!-- type -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="type" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white text-sm  cursor-text text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="type" v-bind="field" :value="true" />
                                Unit Variant
                            </label>
                        </Field>
                        <ErrorMessage name="type" as="p" v-slot="{ message }"
                        class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                            fill="#B3261E" />
                        </svg>
                        <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- measurement -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="measurement" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="measurement" v-bind="field" :value="true" />
                                Measurement <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="measurement" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- bedrooms -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="bedrooms" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="bedrooms" v-bind="field" :value="true" />
                                Bedrooms <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="bedrooms" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- bathrooms -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="bathrooms" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="bathrooms" v-bind="field" :value="true" />
                                Bathrooms <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="bathrooms" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- status -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="status" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="status" v-bind="field" :value="true" />
                                Status <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="status" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- price -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="price" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="price" v-bind="field" :value="true" />
                                Price <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="price" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- style -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="style" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="style" v-bind="field" :value="true" />
                                Style <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="style" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- floor_id -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="floor_id" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="floor_id" v-bind="field" :value="true" />
                                Floor Name <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="floor_id" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- building_id -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="building_id" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="building_id" v-bind="field" :value="true" />
                                Building Name <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="building_id" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- maid -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="maid" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="maid" v-bind="field" :value="true" />
                                Maid <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="maid" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- view -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="view" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="view" v-bind="field" :value="true" />
                                View <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="view" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- units -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="units" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="units" v-bind="field" :value="true" />
                                Units <strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="units" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                    <!-- favIcon -->
                    <div v-if="selected_unitCard === 'custom'" class="flex flex-col justify-start items-start">
                        <Field v-slot="{ field }" name="favIcon" type="checkbox" :value="true" :unchecked-value="false">
                            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                                <input type="checkbox" name="favIcon" v-bind="field" :value="true" />
                                Favorites Icon<strong>*</strong>
                            </label>
                        </Field>
                        <ErrorMessage name="favIcon" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                                fill="#B3261E" />
                            </svg>
                            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                        </ErrorMessage>
                    </div>
                </div>
                <Button id="editCardDetails" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
            </Form>
            <div class="flex justify-start items-center gap-3">
                <label for="editCardDetails"
                :class="['bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] px-4 h-10 m-0 cursor-pointer']">
                Save <Spinner v-if="loader" /></label>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>
