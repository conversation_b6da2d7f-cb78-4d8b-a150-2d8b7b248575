<script setup>
import { ref } from 'vue';
import { XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { updateSettingsFile } from '../../../api/projects/assets/index.ts';
import { EditProject } from '../../../api/projects/index.ts';
import { hologramSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form, useForm } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import { getUploadedStorageLink, processFirebaseFile, ShortenUrl } from '../../../helpers/helpers.ts';
import { getCookie } from '../../../helpers/domhelper';
import TagModal from './TagModal.vue';
import Spinner from '@/components/common/Spinner.vue';
import DnDFileUploader from '@/components/common/DnDFileUploader.vue';
const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const initialData = ref(null);
const shortFileUrl = ref(false);
const type = ref();
const hologramFile = ref(null);
const holoLogo = ref({
  preview: null,
  fileData: null,
});
const holoThumbnail = ref({
  preview: null,
  fileData: null,
});
const typeRef = ref();
const projectType = ref([]);
const brRef = ref([]);
const tagNames = ref([]);
const hologramSettingsLoader = ref(false), viewProjectLogo = ref(), viewThumbnail = ref(), viewFile = ref();
const loader = ref(false);
const showTagModal = ref(false);
const { resetForm, setFieldValue } = useForm();
const formKey = ref(0);
const isEdited = ref(false);

/* Methods */
async function generateShortUrl (url, type) {
  console.log(url);
  await ShortenUrl(url).then((res) => {
    if (type === 'file'){
      shortFileUrl.value = res;
    }
  });
  return;
}

const trackChanges = () => {
  isEdited.value = true;
};

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      name: (data.name ? data.name : null),
      project_location: (data.projectSettings?.hologram?.project_location ? data.projectSettings?.hologram?.project_location : null),
      amount: (data.projectSettings?.hologram?.amount ? data.projectSettings?.hologram?.amount : null),
      project_type: (data.projectSettings?.hologram?.project_type ? data.projectSettings?.hologram?.project_type : null),
      bedrooms: (data.projectSettings?.hologram?.bedrooms ? data.projectSettings?.hologram?.bedrooms : null),
      thumbnail: (data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null),
      file: (data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null),
      tags: (data.projectSettings?.hologram?.tags ? data.projectSettings?.hologram?.tags : null),
      project_logo: (data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null),
    };

    // Form Initial Values
    initialData.value = {
      name: (data.name ? data.name : null),
      project_location: (data.projectSettings?.hologram?.project_location ? data.projectSettings?.hologram?.project_location : null),
      amount: (data.projectSettings?.hologram?.amount ? data.projectSettings?.hologram?.amount : null),
      project_type: (data.projectSettings?.hologram?.project_type ? data.projectSettings?.hologram?.project_type : null),
      bedrooms: (data.projectSettings?.hologram?.bedrooms ? data.projectSettings?.hologram?.bedrooms : null),
      thumbnail: (data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null),
      file: (data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null),
      tags: (data.projectSettings?.hologram?.tags ? data.projectSettings?.hologram?.tags : null),
      project_logo: (data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null),
    };

    hologramFile.value = data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null;
    holoLogo.value.preview = data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null;
    holoLogo.value.fileData = data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null;
    holoThumbnail.value.preview = data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null;
    holoThumbnail.value.fileData = data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null;

    if (data?.projectSettings?.hologram?.project_logo) {
      processFirebaseFile(data.projectSettings.hologram.project_logo).then((result) => {
        if (result) {
          viewProjectLogo.value = result;
        }
      });
    }
    if (data?.projectSettings?.hologram?.thumbnail) {
      processFirebaseFile(data.projectSettings.hologram.thumbnail).then((result) => {
        if (result) {
          viewThumbnail.value = result;
        }
      });
    }
    if (data?.projectSettings?.hologram?.file) {
      processFirebaseFile(data.projectSettings.hologram.file).then((result) => {
        if (result) {
          viewFile.value = result;
        }
      });
    }

    if (data.projectSettings?.hologram?.tags) {
      tagNames.value = [...data.projectSettings.hologram.tags];
    }

    if (data.projectSettings?.hologram?.file){
      generateShortUrl(data.projectSettings?.hologram?.file, 'file');
    }

    if (data.projectSettings?.hologram?.bedrooms){
      brRef.value = [...data.projectSettings.hologram.bedrooms];
    }

    if (data.projectSettings?.hologram?.project_type){
      typeRef.value = data.projectSettings?.hologram?.project_type;
      projectType.value = [ data.projectSettings?.hologram?.project_type];
    }

  }
};

const updateValuesCallback = (res) => {
  if (res){
    projectStore.settings.projectSettings[projectSettingsFormTypes.HOLOGRAM] = res.projectSettings[projectSettingsFormTypes.HOLOGRAM]; // update to store
    setupDataCallBack(res); // update the values
  }
};

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleSubmit = async (val) => {

  console.log(val);
  loader.value = true;
  const prevData = previousData.value; // prevData track source
  const newCompareObj = { ...val, bedrooms: brRef.value, tags: tagNames.value }; // form values

  if (type.value) {
    delete type.value.newCompareObj;
  }
  const parms = frameParms(prevData, newCompareObj);
  if (Object.keys(parms).length === 0) {
    isEdited.value = false;
    return;
  }

  hologramSettingsLoader.value = true; // loader

  // Separate name from hologram settings
  const { name, ...hologramParms } = parms;

  // Handle name update separately using EditProject API
  if (name && prevData.name !== name) {
    try {
      const editProjectPayload = {
        id: project_Id.value,
        name: name,
      };

      const editResult = await EditProject(editProjectPayload);
      if (editResult) {
        const projectData = editResult;
        Object.assign(projectStore.settings, projectData);
        previousData.value.name = name;
      }
    } catch (err) {
      console.log("Error updating project name:", err);
    }
  }

  if (Object.keys(hologramParms).length > 0) {
    if (hologramParms.project_logo || hologramParms.thumbnail || hologramParms.file) {

      const organization = getCookie('organization');
      const payload1 = new FormData();
      payload1.append('project_id', project_Id.value);

      // Add files to FormData if they exist
      hologramParms.thumbnail && payload1.append('thumbnail', hologramParms.thumbnail);
      hologramParms.project_logo && payload1.append('hologram_project_logo', hologramParms.project_logo);

      let filepath;
      if (hologramParms.file){
        const stampFileName = hologramParms.file.name;
        filepath = `CreationtoolAssets/${organization}/projects/${project_Id.value}/${projectSettingsFormTypes.HOLOGRAM}/${stampFileName}`; // File path formation
      }
      const payload2 = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.HOLOGRAM]: {},
        },
      };

      // Add all form data to hologram object
      Object.keys(hologramParms).forEach((key) => {
        payload2.query[projectSettingsFormTypes.HOLOGRAM][key] = hologramParms[key];
      });
      payload2.query[projectSettingsFormTypes.HOLOGRAM].bedrooms = brRef.value;

      if (hologramParms.file){
        getUploadedStorageLink(hologramParms.file, filepath).then((res) => {
          if (hologramParms.thumbnail || hologramParms.project_logo){
            updateSettingsFile(payload1).then((data) => {
              payload2.query[projectSettingsFormTypes.HOLOGRAM].thumbnail = data.thumbnail;
              payload2.query[projectSettingsFormTypes.HOLOGRAM].hologram_project_logo = data.hologram_project_logo;
              payload2.query[projectSettingsFormTypes.HOLOGRAM].file = res;
              UpdateProjectSettings(payload2).then((res) => {
                updateValuesCallback(res);
              }).catch((err) => {
                console.log(err);
              }).finally(() => {
                loader.value = false;
                hologramSettingsLoader.value = false;
                isEdited.value = false;
              });

            });
          } else {
            payload2.query[projectSettingsFormTypes.HOLOGRAM].file = res;
            UpdateProjectSettings(payload2).then((res) => {
              updateValuesCallback(res);
            }).catch((err) => {
              console.log(err);
            }).finally(() => {
              loader.value = false;
              hologramSettingsLoader.value = false;
              isEdited.value = false;
            });
          }
        });
      } else {
        updateSettingsFile(payload1).then((data) => {
          data.thumbnail && (payload2.query[projectSettingsFormTypes.HOLOGRAM].thumbnail = data.thumbnail);
          data.hologram_project_logo && (payload2.query[projectSettingsFormTypes.HOLOGRAM].hologram_project_logo = data.hologram_project_logo);
          delete payload2.query[projectSettingsFormTypes.HOLOGRAM].project_logo;

          UpdateProjectSettings(payload2).then((res) => {
            updateValuesCallback(res);
          }).catch((err) => {
            console.log(err);
          }).finally(() => {
            loader.value = false;
            hologramSettingsLoader.value = false;
            isEdited.value = false;
          });
        });
      }
    } else {
      const formData = new FormData();
      formData.append('project_id', project_Id.value);
      const payload = {
        project_id: project_Id.value,
        query: { [projectSettingsFormTypes.HOLOGRAM]: hologramParms },
      };

      UpdateProjectSettings(payload).then((result) => {
        if (result) {
          projectStore.settings.projectSettings[projectSettingsFormTypes.HOLOGRAM] = result.projectSettings[projectSettingsFormTypes.HOLOGRAM]; // update to store
          setupDataCallBack(result); // update the values
        }
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        loader.value = false;
        hologramSettingsLoader.value = false;
        isEdited.value = false;
      });
    }
  } else {
    // No hologram parameters to update, just finish
    loader.value = false;
    hologramSettingsLoader.value = false;
    isEdited.value = false;
  }

};

const openSlotModal = (tagType) => {
  type.value = tagType;
  showTagModal.value = true;
};

const handleTagModalConfirm = (tags) => {
  showTagModal.value = false;
  console.log("tags", tags);

  // Update the appropriate field based on which one opened the modal
  if (type.value === 'bedrooms') {
    brRef.value = [...tags];
    trackChanges();
  } else {
    tagNames.value = [...tags];
    trackChanges();
  }
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

function hexToRgb (hex) {
  if (hex) {
    hex = hex.replace(/^#/, '');
    if (hex.length === 3) {
      hex = hex.split('').map((c) => c + c).join('');
    }
    const num = parseInt(hex, 16);
    return {
      r: (num >> 16) & 255,
      g: (num >> 8) & 255,
      b: num & 255,
    };
  }
}

function getLuminance ({ r, g, b }) {
  const [R, G, B] = [r, g, b].map((v) => {
    v /= 255;
    return v <= 0.03928
      ? v / 12.92
      : Math.pow((v + 0.055) / 1.055, 2.4);
  });
  return (0.2126 * R) + (0.7152 * G) + (0.0722 * B);
}

function adjustColor ({ r, g, b }, amount, lighten = true) {
  if (lighten) {
    r = Math.min(255, Math.floor(r + ((255 - r) * amount)));
    g = Math.min(255, Math.floor(g + ((255 - g) * amount)));
    b = Math.min(255, Math.floor(b + ((255 - b) * amount)));
  } else {
    r = Math.max(0, Math.floor(r * (1 - amount)));
    g = Math.max(0, Math.floor(g * (1 - amount)));
    b = Math.max(0, Math.floor(b * (1 - amount)));
  }
  return `rgb(${r}, ${g}, ${b})`;
}

function getTagStyle (hexColor) {
  const rgb = hexToRgb(hexColor);
  const luminance = getLuminance(rgb);
  const isLight = luminance > 0.7;
  const bgColor = adjustColor(rgb, 0.6, !isLight);
  return {
    color: hexColor,
    backgroundColor: bgColor,
  };
}

const handleCancel = () => {
  const newInitialData = { ...initialData.value };
  initialData.value = newInitialData;
  resetForm({ values: newInitialData });
  formKey.value++; // Force remount
  setFieldValue('name', newInitialData.name);
  setFieldValue('project_location', newInitialData.project_location);
  setFieldValue('amount', newInitialData.amount);
  setFieldValue('project_type', newInitialData.project_type);
  setFieldValue('bedrooms', newInitialData.bedrooms);
  setFieldValue('thumbnail', newInitialData.thumbnail);
  setFieldValue('file', newInitialData.file);
  setFieldValue('tags', newInitialData.tags);
  setFieldValue('project_logo', newInitialData.project_logo);
  brRef.value = previousData.value.bedrooms ? [...previousData.value.bedrooms] : [];
  tagNames.value = previousData.value.tags ? [...previousData.value.tags] : [];
  isEdited.value = false;
};

defineExpose({
  isEdited,
  handleCancel,
});

</script>

<template>
  <div class="flex flex-col sm:flex-row sm:gap-8 gap-1 sm:w-[calc(100%-240px)] relative mt-3">
    <!-- Form Section -->
    <div class="flex flex-col w-full sm:w-[50vw] sm:mb-24 h-full order-1 sm:order-1">
      <!-- Form -->
      <Form :key="formKey" class="grid grid-cols-2 justify-start items-start sm:w-[50vw] gap-8" @submit="val => handleSubmit(val)"
        :initial-values="initialData" :validation-schema="hologramSettingsSchema">
        <!-- name -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Project Name*</span>
            <span class="text-xs text-gray-500">This name will appear on the card as the project name.</span>
          </div>
          <Field type="text" name="name" id="name" class="input-primary w-full bg-[#F9FAFB]"
            placeholder="Enter Location" @input="trackChanges"/>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="name" />
        </div>
        <!-- project_location -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Location*</span>
            <span class="text-xs text-gray-500">This location will appear on the card as the project location.</span>
          </div>
          <Field type="text" name="project_location" id="project_location" class="input-primary w-full bg-[#F9FAFB]"
            placeholder="Enter Location" @input="trackChanges"/>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="project_location" />
        </div>
        <!-- project_type -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Project Type*</span>
            <span class="text-xs text-gray-500">This type will appear on the card as the project type.</span>
          </div>
          <Field type="text" name="project_type" id="project_type" class="input-primary w-full bg-[#F9FAFB]"
            placeholder="Enter Project type" @input="trackChanges"/>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="project_type" />
        </div>
        <!-- bedrooms -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Bedrooms Types*</span>
            <span class="text-xs text-gray-500">This bedroom type will appear on the card as the bedroom type.</span>
          </div>
          <div class="border rounded-lg p-3 w-full flex gap-2 flex-col">
            <Field name="bedrooms" :model-value="brRef" v-slot="{ field }">
              <div v-if="brRef.length > 0" v-bind="field">
                <div
                  class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                  <template v-if="brRef.length > 0">
                    <span
                      class="inline-flex items-center px-3 py-1 rounded text-sm font-medium mr-2 mb-2 gap-2 bg-gray-200"
                      v-for="(tag, index) in brRef" :key="index">
                      {{ tag }}
                      <svg class="cursor-pointer" @click="openSlotModal('bedrooms')" xmlns="http://www.w3.org/2000/svg"
                        width="10" height="10" viewBox="0 0 10 10" fill="none">
                        <g clip-path="url(#clip0_7536_30816)">
                          <path
                            d="M6.91804 3.78807L6.91553 3.79157L3.34686 7.3606L4.41641 8.42976L7.98809 4.85823L6.91804 3.78807Z"
                            fill="#6B7280" />
                          <path
                            d="M1.56977 5.58334L2.63932 6.6535L6.2085 3.08446L6.212 3.08196L5.14145 2.0113L1.56977 5.58334Z"
                            fill="#6B7280" />
                          <path
                            d="M0.997742 6.42546L0.025694 9.3414C-0.0343089 9.52093 0.0126933 9.71946 0.1467 9.85298C0.241705 9.94849 0.369711 10 0.500217 10C0.55322 10 0.606723 9.9915 0.658225 9.974L3.57387 9.00185L0.997742 6.42546Z"
                            fill="#6B7280" />
                          <path
                            d="M9.41116 0.588088C8.62662 -0.196029 7.34956 -0.196029 6.56502 0.588088L5.84898 1.3042L8.69562 4.15112L9.41166 3.43502C10.1962 2.6504 10.1962 1.37271 9.41116 0.588088Z"
                            fill="#6B7280" />
                        </g>
                        <defs>
                          <clipPath id="clip0_7536_30816">
                            <rect width="10" height="10" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      <XMarkIcon class="w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer" @click="() => {
                        brRef.splice(index, 1); trackChanges();
                      }" />
                    </span>
                  </template>
                </div>
              </div>
              <span v-if="!brRef.length" class="text-xs text-gray-500">No bedroom tag is added</span>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button type="button" @click="openSlotModal('bedrooms')" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                    <path
                      d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z"
                      fill="#1A56DB" />
                  </svg>
                  Add Tag
                </button>
              </div>
            </Field>
          </div>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="bedrooms" />
        </div>
        <!-- project_logo -->
        <div class="flex flex-col gap-2 relative">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Upload Logo*</span>
            <span class="text-xs text-gray-500">This logo will appear on project card.</span>
          </div>
          <Field name="project_logo" id="project_logo" :model-value="holoLogo.fileData || viewProjectLogo" @input="trackChanges">
            <DnDFileUploader inputType="image/*" class="!h-[180px]"
              inputPlaceholder="Click to upload or drag & drop files here"
              @fileData="(val) => { holoLogo.fileData = val; trackChanges(); }" :previousFileData="viewProjectLogo"
              :key="viewProjectLogo" />
          </Field>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="project_logo" />
        </div>
        <!-- thumbnail -->
        <div class="flex flex-col gap-2 relative">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Upload Project Card Image*</span>
            <span class="text-xs text-gray-500">This image will appear on project card.</span>
          </div>
          <Field name="thumbnail" id="thumbnail" :model-value="holoThumbnail.fileData || viewThumbnail" @input="trackChanges">
            <DnDFileUploader inputType="image/*" class="!h-[180px]"
              inputPlaceholder="Click to upload or drag & drop files here"
              @fileData="(val) => { holoThumbnail.fileData = val; trackChanges(); }" :previousFileData="viewThumbnail"
              :key="viewThumbnail" />
          </Field>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="thumbnail" />
        </div>
        <!-- file -->
        <div class="flex flex-col gap-2 relative">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Upload Project Background Image*</span>
            <span class="text-xs text-gray-500">This image will appear as Background image project.</span>
          </div>
          <Field name="file" id="file" :model-value="hologramFile || viewFile" @input="trackChanges">
            <DnDFileUploader inputType="image/*,video/*,.glb" class="!h-[180px]"
              inputPlaceholder="Click to upload or drag & drop files here" @fileData="(val) => { hologramFile = val; trackChanges(); console.log('hologramFile', hologramFile, val) }"
              :previousFileData="viewFile" :key="viewFile" :onlyVideo="true"/>
          </Field>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="file" />
        </div>
        <!-- tags -->
        <div class="flex flex-col justify-start items-start w-full gap-2">
          <div class="flex flex-col gap-[2px]">
            <span class="text-sm">Add Tag*</span>
            <span class="text-xs text-gray-500">This tag will appear on card </span>
          </div>
          <div class="border rounded-lg p-3 w-full">
            <Field name="tags" :model-value="tagNames" v-slot="{ field }">
              <div v-if="tagNames.length > 0" v-bind="field">
                <div
                  class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                  <template v-if="tagNames.length > 0">
                    <span class="inline-flex items-center px-3 py-1 rounded text-sm font-medium mr-2 mb-2 gap-2"
                      :style="getTagStyle(tag.color)" v-for="(tag, index) in tagNames" :key="index">
                      {{ tag.name }}
                      <svg class="cursor-pointer" @click="openSlotModal('tags')" xmlns="http://www.w3.org/2000/svg"
                        width="10" height="10" viewBox="0 0 10 10" fill="none">
                        <g clip-path="url(#clip0_7536_30816)">
                          <path
                            d="M6.91804 3.78807L6.91553 3.79157L3.34686 7.3606L4.41641 8.42976L7.98809 4.85823L6.91804 3.78807Z"
                            fill="#6B7280" />
                          <path
                            d="M1.56977 5.58334L2.63932 6.6535L6.2085 3.08446L6.212 3.08196L5.14145 2.0113L1.56977 5.58334Z"
                            fill="#6B7280" />
                          <path
                            d="M0.997742 6.42546L0.025694 9.3414C-0.0343089 9.52093 0.0126933 9.71946 0.1467 9.85298C0.241705 9.94849 0.369711 10 0.500217 10C0.55322 10 0.606723 9.9915 0.658225 9.974L3.57387 9.00185L0.997742 6.42546Z"
                            fill="#6B7280" />
                          <path
                            d="M9.41116 0.588088C8.62662 -0.196029 7.34956 -0.196029 6.56502 0.588088L5.84898 1.3042L8.69562 4.15112L9.41166 3.43502C10.1962 2.6504 10.1962 1.37271 9.41116 0.588088Z"
                            fill="#6B7280" />
                        </g>
                        <defs>
                          <clipPath id="clip0_7536_30816">
                            <rect width="10" height="10" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      <XMarkIcon class="w-4 h-4 text-gray-400 hover:text-red-500 cursor-pointer"
                        @click="() => { tagNames.splice(index, 1); trackChanges() }" />
                    </span>
                  </template>
                </div>
              </div>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button type="button" @click="openSlotModal('tags')" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                    <path
                      d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z"
                      fill="#1A56DB" />
                  </svg>
                  Add Tag
                </button>
              </div>
            </Field>
          </div>
          <ErrorMessage as="p" class="text-sm text-rose-500" name="tags" />
        </div>
        <Button id="editHologramSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
      </Form>
    </div>
    <!-- Preview Section (single instance, order changes with screen size) -->
    <div class="w-full sm:w-auto flex flex-col items-start justify-start pt-4 gap-2 order-2 sm:order-2 sm:flex-1 mb-24 sm:mb-0">
      <span class="text-sm font-semibold text-left">Preview *</span>
      <span class="text-xs text-gray-500">View a sample card layout with example details.</span>
      <div class="w-[343px] h-[627px] bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col relative border border-gray-200" style="font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;">
        <div class="relative w-full h-full">
          <img v-if="viewThumbnail" :src="viewThumbnail.url" alt="Card Image" class="object-cover w-full h-full absolute inset-0" />
          <div v-else class="w-full h-full flex items-center justify-center text-gray-400 bg-gray-100 absolute inset-0">No Image</div>
          <div class="absolute inset-x-0 bottom-0 h-2/5" style="background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.85) 100%);"></div>
          <div class="absolute top-8 left-1/2 transform -translate-x-1/2 z-10 flex flex-col items-center">
            <img v-if="viewProjectLogo" :src="viewProjectLogo.url" alt="Logo" class="h-10 object-contain" />
            <span v-else class="text-white text-lg font-light tracking-widest uppercase">LOGO</span>
          </div>
          <div class="absolute left-0 right-0 bottom-0 px-4 pb-6 pt-10 flex flex-col z-10">
            <div class="text-2xl font-bold text-white mb-1 leading-tight">{{ initialData?.name || 'The Acres' }}</div>
            <div class="text-sm text-white/80 mb-1">{{ initialData?.project_location || 'Dubai Sport City , Dubai' }}</div>
            <button class="w-full bg-[#E5C97B] text-[#3A2E13] font-semibold py-3 rounded-lg mt-2 shadow text-base hover:bg-[#f7d08a] transition">View Project</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Save/Cancel Buttons -->
    <div v-if="isEdited"
        class="fixed bottom-0 left-0 w-full sm:left-[240px] sm:w-[calc(100%-240px)] z-50 bg-white dark:bg-bg-1000
              flex justify-end sm:justify-center items-center gap-3 px-8 py-4">
      <label for="editHologramSettings"
        class="bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150
              rounded-lg flex flex-row justify-center items-center gap-[9px]
              px-4 h-10 m-0 cursor-pointer">
        Save
        <Spinner v-if="loader" />
      </label>
      <button type="reset"
        class="bg-gray-100 dark:bg-bg-1000 text-gray-500 dark:text-txt-150
              rounded-lg flex flex-row justify-center items-center gap-[9px]
              px-4 h-10 m-0 cursor-pointer"
        @click="handleCancel">
        Cancel
      </button>
    </div>
    <TagModal v-if="showTagModal" v-model="showTagModal" :type="type === 'bedrooms' ? 'tag' : 'colorTags'" :initialTags="type === 'bedrooms' ? brRef : tagNames" @confirm="handleTagModalConfirm" />
  </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}

.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
  }

  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

</style>
