<script setup>
import { ref } from 'vue';
import { Form } from 'vee-validate';
import { ProjectStore } from '../../../store/project.ts';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { useRoute } from 'vue-router';
import { UpdateProjectSettings } from '@/api/projects/settings';
import Modal from '@/components/common/Modal/Modal.vue';

const metadata = ref([]);
const projectStore = ProjectStore();
const loader = ref(false);
const isEdit = ref(false);
const previousData = ref(null);
const route = useRoute();
const project_Id = ref(route.params.project_id);
const showTagModal = ref(false);
const modalMetadata = ref([]);

const frameParms = (sourceObj, compareObj) => {
  if (!sourceObj) {
    return compareObj;
  }

  // If both objects are empty, return false
  if (Object.keys(sourceObj).length === 0 && Object.keys(compareObj).length === 0) {
    return false;
  }

  // If the objects are exactly the same, return false
  if (JSON.stringify(sourceObj) === JSON.stringify(compareObj)) {
    return false;
  }

  // For all other cases, return the compareObj
  return compareObj;
};

const setupDataCallBack = (values) => {
  console.log(values);
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      metadata: (data.projectSettings?.metadata ? data.projectSettings?.metadata : null),
    };

    if (data.projectSettings?.metadata){
      if (Object.keys(data.projectSettings?.metadata).length){
        Object.entries(data.projectSettings?.metadata).forEach(([key, value]) => metadata.value = [{key, value}, ...metadata.value]);
      }
    }
  }
};

const handleForm = () => {  // To handle unit addition or updation
  const metadataObj = {};  // To transform metadata array into one object in key and value pair
  metadata.value.forEach((elem) => {
    if (elem.key && elem.value){
      metadataObj[elem.key] = elem.value;
    }
  });
  if (frameParms(previousData.value.metadata, metadataObj)){
    loader.value = true;
    const payload = {
      project_id: project_Id.value,
      query: {
        [projectSettingsFormTypes.METADATA]: frameParms(previousData.value.metadata, metadataObj),
      },
    };
    console.log(payload);
    UpdateProjectSettings(payload)
      .then((res) => {
        if (res){
          metadata.value = [];
          projectStore.settings.projectSettings[projectSettingsFormTypes.METADATA] = res.projectSettings[projectSettingsFormTypes.METADATA];
          setupDataCallBack(res);
          isEdit.value = false;
          loader.value = false;
        }
      })
      .catch((error) => {
        console.log(error);
        loader.value = false;
      });
  }
};

const openAddMetadataModal = () => {
  // Copy current metadata to modal state
  modalMetadata.value = metadata.value.length
    ? metadata.value.map((item) => ({ ...item }))
    : [{ key: '', value: '' }];
  showTagModal.value = true;
};

const addModalPair = () => {
  modalMetadata.value.push({ key: '', value: '' });
};

const removeModalPair = (index) => {
  modalMetadata.value.splice(index, 1);
};

const handleModalMetadataChange = (e, ind) => {
  modalMetadata.value[ind][e.target.name] = e.target.value;
};

const confirmModalMetadata = () => {
  // Filter out empty pairs
  metadata.value = modalMetadata.value.filter(
    (item) => item.key && item.value,
  );
  showTagModal.value = false;
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
  <div class="flex flex-col justify-start items-start my-3 ml-1">
    <!-- Headers -->
    <div class="flex flex-col w-full">
      <!-- Form -->
      <Form class="grid grid-cols-2 justify-start items-start w-[75%] gap-8" @submit="handleForm">
        <div class="flex flex-col justify-start items-start w-full">
          <label class="text-sm text-black">Tags</label>
          <div class="border rounded-lg p-3 w-full">
            <Field name="tags" :model-value="metadata" >
              <div class="flex flex-col gap-2">
                <div v-for="(elem, index) in metadata" :key="index" class="flex gap-4 mb-2 items-center text-sm">
                  {{elem .key}} - {{ elem.value }}
                </div>
                </div>
              <div class="flex gap-2 text-sm items-center justify-stretch my-2">
                <button @click="openAddMetadataModal" class="flex items-center gap-2 text-blue-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                    <path d="M10.2669 5.96662H6.53353V2.23328C6.53353 2.09184 6.47734 1.95618 6.37732 1.85616C6.2773 1.75614 6.14164 1.69995 6.0002 1.69995C5.85875 1.69995 5.72309 1.75614 5.62307 1.85616C5.52305 1.95618 5.46686 2.09184 5.46686 2.23328V5.96662H1.73353C1.59208 5.96662 1.45642 6.02281 1.35641 6.12283C1.25639 6.22285 1.2002 6.3585 1.2002 6.49995C1.2002 6.6414 1.25639 6.77706 1.35641 6.87707C1.45642 6.97709 1.59208 7.03328 1.73353 7.03328H5.46686V10.7666C5.46686 10.9081 5.52305 11.0437 5.62307 11.1437C5.72309 11.2438 5.85875 11.3 6.0002 11.3C6.14164 11.3 6.2773 11.2438 6.37732 11.1437C6.47734 11.0437 6.53353 10.9081 6.53353 10.7666V7.03328H10.2669C10.4083 7.03328 10.544 6.97709 10.644 6.87707C10.744 6.77706 10.8002 6.6414 10.8002 6.49995C10.8002 6.3585 10.744 6.22285 10.644 6.12283C10.544 6.02281 10.4083 5.96662 10.2669 5.96662Z" fill="#1A56DB"/>
                  </svg>
                  Add Tag
                </button>
              </div>
            </Field>
          </div>
        </div>
        <!-- <Menu
              as="div"
              class="relative h-fit flex-col justify-start items-start inline-flex bg-inherit"
            >
                <MenuItems
                  style="width: 300px; background-color: #737373 !important"
                  class="z-10 mt-2 top-[4.4rem] rounded-md p-1 bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <div
                    class="flex items-center gap-1.5 mb-1"
                    v-for="(elem, index) in metadata"
                    :key="index"
                    v-bind="field"
                  >
                    <input
                      class="rounded-sm w-6 p-1 text-sm flex-1"
                      type="text"
                      name="key"
                      :value="elem.key"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="key"
                    />
                    <input
                      class="rounded-sm w-7 p-1 text-sm flex-1"
                      type="text"
                      name="value"
                      :value="elem.value"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="value"
                    />
                    <button
                      class="bg-[#1C74D0] text-white rounded-sm text-sm p-1"
                      type="button"
                      @click="removePair(index)"
                    >
                      remove
                    </button>
                  </div>
                  <button
                    class="w-full bg-[#1C74D0] text-white rounded-sm text-sm p-1 mt-0.5"
                    type="button"
                    @click="addPair(event)"
                  >
                    + Add
                  </button>
                </MenuItems>
        </Menu> -->
        <button
              type="submit"
              class="proceed-btn-primary !hidden"
              id="addMetadata"
            >
              Save
              <Spinner v-if="loader" />
        </button>
      </Form>
      <div class="flex justify-start items-center gap-3">
          <label for="addMetadata"
          :class="['bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] px-4 h-10 m-0 cursor-pointer',(loader && `pointer-events-none opacity-50`)]">
          <div v-if="loader" class="loader"></div>
          Save </label>
      </div>
    </div>
  </div>
  <Modal :open="showTagModal" @update:modelValue="showTagModal = $event">
    <div class="modal-backdrop">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="font-bold text-lg">Add Metadata</h3>
          <button class="close-btn" @click="showTagModal = false">×</button>
        </div>
        <div class="flex flex-col gap-2 mt-4">
          <div class="flex font-semibold mb-2">
            <span class="w-1/2">Key</span>
            <span class="w-1/2">Value</span>
          </div>
          <div v-for="(pair, idx) in modalMetadata" :key="idx" class="flex items-center gap-2 mb-2">
            <input
              class="border rounded p-2 text-sm w-1/2"
              placeholder="Key"
              name="key"
              :value="pair.key"
              @input="e => handleModalMetadataChange(e, idx)"
            />
            <input
              class="border rounded p-2 text-sm w-1/2"
              placeholder="Value"
              name="value"
              :value="pair.value"
              @input="e => handleModalMetadataChange(e, idx)"
            />
            <button @click="removeModalPair(idx)" v-if="modalMetadata.length > 1">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                            <g clip-path="url(#clip0_7502_34218)">
                                <path d="M6.37109 1H9.62988C9.92401 1 10.2097 1.12055 10.4229 1.34082C10.6366 1.56173 10.7598 1.86528 10.7598 2.18457V4.36816H14.5186C14.5966 4.36816 14.675 4.40052 14.7354 4.46289C14.7963 4.52588 14.834 4.61499 14.834 4.71094C14.8339 4.80673 14.7962 4.89511 14.7354 4.95801C14.6749 5.02043 14.5966 5.05273 14.5186 5.05273H13.2041V14.8154C13.2041 15.1347 13.0809 15.4383 12.8672 15.6592C12.654 15.8794 12.3683 16 12.0742 16H3.92578C3.63177 15.9999 3.34591 15.8794 3.13281 15.6592C2.91914 15.4383 2.79688 15.1347 2.79688 14.8154V5.05273H1.48145C1.40349 5.05264 1.32496 5.02034 1.26465 4.95801C1.20397 4.89513 1.1671 4.80657 1.16699 4.71094C1.16699 4.61511 1.20384 4.52586 1.26465 4.46289C1.32496 4.40056 1.40349 4.36826 1.48145 4.36816H5.24121V2.18457C5.24121 1.86528 5.36437 1.56173 5.57812 1.34082C5.7646 1.14823 6.00628 1.03198 6.26074 1.00586L6.37109 1ZM6.37109 5.89453C6.01699 5.89453 5.68075 6.04055 5.43555 6.29395C5.19088 6.54681 5.05566 6.88657 5.05566 7.2373V13.1318C5.05573 13.4825 5.19094 13.8224 5.43555 14.0752C5.68072 14.3284 6.01716 14.4736 6.37109 14.4736C6.72507 14.4735 7.06152 14.3285 7.30664 14.0752C7.55125 13.8224 7.68548 13.4825 7.68555 13.1318V7.2373C7.68555 6.88674 7.55109 6.54677 7.30664 6.29395C7.06152 6.04062 6.72507 5.89464 6.37109 5.89453ZM9.62988 5.89453C9.27578 5.89455 8.93953 6.04054 8.69434 6.29395C8.44979 6.54679 8.31543 6.88666 8.31543 7.2373V13.1318C8.3155 13.4825 8.44973 13.8224 8.69434 14.0752C8.93951 14.3285 9.27587 14.4736 9.62988 14.4736C9.98389 14.4736 10.3202 14.3285 10.5654 14.0752C10.81 13.8224 10.9443 13.4825 10.9443 13.1318V7.2373C10.9443 6.88663 10.81 6.5468 10.5654 6.29395C10.3202 6.04052 9.98401 5.89453 9.62988 5.89453ZM5.87109 4.36816H10.1299V1.68457H5.87109V4.36816Z" fill="#F05252" stroke="#F05252"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_7502_34218">
                                <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                                </clipPath>
                            </defs>
                        </svg>
            </button>
            <button @click="addModalPair" v-if="idx === modalMetadata.length - 1">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                            <path d="M7.99902 1C9.98759 1.0023 11.8946 1.79309 13.3008 3.19922C14.6191 4.5175 15.3963 6.27573 15.4902 8.12891L15.5 8.50098C15.4998 9.984 15.0603 11.4339 14.2363 12.667C13.4122 13.9003 12.2405 14.861 10.8701 15.4287C9.49973 15.9963 7.99191 16.1448 6.53711 15.8555C5.08225 15.5661 3.74518 14.8526 2.69629 13.8037C1.6474 12.7548 0.93392 11.4177 0.644531 9.96289C0.355194 8.50809 0.503657 7.00027 1.07129 5.62988C1.63895 4.2595 2.59968 3.08776 3.83301 2.26367C5.0661 1.43974 6.516 1.00019 7.99902 1ZM8 3.58594C7.64406 3.58594 7.30247 3.72684 7.05078 3.97852C6.7991 4.2302 6.65822 4.57181 6.6582 4.92773V7.1582H4.42773C4.07181 7.15822 3.7302 7.2991 3.47852 7.55078C3.22684 7.80247 3.08594 8.14407 3.08594 8.5C3.08594 8.85594 3.22684 9.19753 3.47852 9.44922C3.73019 9.7009 4.07181 9.84178 4.42773 9.8418H6.6582V12.0723C6.65822 12.4282 6.7991 12.7698 7.05078 13.0215C7.30247 13.2732 7.64406 13.4141 8 13.4141C8.35594 13.4141 8.69753 13.2732 8.94922 13.0215C9.2009 12.7698 9.34178 12.4282 9.3418 12.0723V9.8418H11.5723C11.9282 9.84178 12.2698 9.7009 12.5215 9.44922C12.7732 9.19753 12.9141 8.85594 12.9141 8.5C12.9141 8.14406 12.7732 7.80247 12.5215 7.55078C12.2698 7.2991 11.9282 7.15822 11.5723 7.1582H9.3418V4.92773C9.34178 4.5718 9.2009 4.2302 8.94922 3.97852C8.69753 3.72684 8.35594 3.58594 8 3.58594Z" fill="#1A56DB" stroke="#1A56DB"/>
                        </svg>
            </button>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <button class="confirm-btn" @click="confirmModalMetadata">Confirm</button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.modal-backdrop {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  min-width: 430px;
  max-width: 45vw;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0;
  padding-left: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.input-time {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 4px 8px;
  width: 135px;
  appearance: auto;
}

.add-btn {
  color: #fff;
  border: none;
  border-radius: 20px;
  width: 28px;
  height: 28px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: 0;
  margin-right: 4px;
}

.confirm-btn {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 24px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
}

.modal-footer {
  border-top: 0;
  padding-top: 16px;
}

.slot-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 80px;
  gap: 8px 8px;
  align-items: center;
}
.slot-grid-header {
  display: contents;
}
.slot-grid-row {
  display: contents;
}
</style>
