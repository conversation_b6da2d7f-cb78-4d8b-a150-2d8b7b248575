<script setup>
import { onMounted, ref } from 'vue';
import { UpdateProjectSettings, GetApplicationList} from '../../../api/projects/settings/index.ts';
import { projectPixelStreamingSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form, useForm } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import Multiselect from 'vue-multiselect';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const initialData = ref(null);
const records = ref([]), arrayOfRecords=ref([]), selected_applicationId=ref([]), loader = ref(false);
const { resetForm, setFieldValue } = useForm();
const formKey = ref(0);
const isEdited = ref(false);
/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const trackChanges = () => {
  isEdited.value = true;
};

const setupDataCallBack = async (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.pixelstreaming?.is_enabled ? data.projectSettings?.pixelstreaming?.is_enabled : false,
      application_id: (data.projectSettings?.pixelstreaming?.application_id ? data.projectSettings?.pixelstreaming?.application_id : null),
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.pixelstreaming?.is_enabled ? data.projectSettings?.pixelstreaming?.is_enabled : false),
      application_id: data.projectSettings?.pixelstreaming?.application_id,
    };
    if (data.projectSettings?.pixelstreaming?.application_id){
      selected_applicationId.value = arrayOfRecords.value.find((elem) => elem.appliId === data.projectSettings?.pixelstreaming?.application_id);
    }
  }
};

const handleSubmit = async (val) => {
  loader.value = true;
  const prevData = previousData.value;
  const newCompareObj = { ...val };
  newCompareObj.application_id = newCompareObj.application_id?.appliId ? newCompareObj.application_id.appliId : null;

  const parms = frameParms(prevData, newCompareObj);
  const payload = {
    project_id: project_Id.value,
    query: {
      [projectSettingsFormTypes.PIXELSTREAMING]: {},
    },
  };

  Object.keys(parms).forEach((key) => {
    payload.query.pixelstreaming[key] = parms[key];
  });

  UpdateProjectSettings(payload).then((res) => {
    if (res){
      projectStore.settings.projectSettings[projectSettingsFormTypes.PIXELSTREAMING] = res.projectSettings[projectSettingsFormTypes.PIXELSTREAMING]; // update to store
      setupDataCallBack(res);  // update the data value
      resolve(res);
    }
  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    loader.value = false;
    isEdited.value = false;
  });
};

const handleCancel = () => {
  const newInitialData = { ...initialData.value };
  initialData.value = newInitialData;
  selected_applicationId.value = arrayOfRecords.value.find((elem) => elem.appliId === previousData.value.application_id);
  // Reset the form
  resetForm({ values: newInitialData });
  formKey.value++; // Force remount
  setFieldValue('application_id', newInitialData.application_id);
  setFieldValue('is_enabled', newInitialData.is_enabled);
  isEdited.value = false;
};

if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

// Initialize
onMounted(async () => {
  await GetApplicationList().then((res) => {
    records.value = res.result.records;
    arrayOfRecords.value = records.value.map((item) => ({
      appliId: item.appliId,
      appliName: item.appliName,
    }));
    if (projectStore.settings){
      setupDataCallBack(projectStore.settings);
    }
  });
});

defineExpose({
  isEdited,
  handleCancel,
});

</script>

<template>
  <div class="sm:w-[calc(100%-240px)] relative mt-3">
    <!-- Headers -->
    <div class="flex flex-col sm:w-[50vw] mb-4 h-full">
      <!-- Form -->
      <Form
        :key="formKey"
        class="grid grid-cols-2 justify-start items-start sm:w-[50vw] gap-8"
        @submit="val => handleSubmit(val)"
        :initial-values="initialData"
        :validation-schema="projectPixelStreamingSettingsSchema"
      >
        <!-- is_enabled -->
        <div class="flex flex-col justify-start items-start">
          <div class="flex items-start w-full justify-between">
                  <div class="flex flex-col gap-1">
                    <div class="text-sm">Pixel Streaming</div>
                    <div class="text-xs text-gray-500">If enabled, you can Stream interactive Experience directly while meeting.</div>
                  </div>
                  <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false" @input="trackChanges">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="sr-only peer"/>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:bg-blue-600 rounded-full peer transition-colors"></div>
                      <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
                    </label>
                  </Field>
          </div>
          <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <!-- application_id -->
        <div class="flex flex-col justify-start items-start gap-1 relative">
          <label class="text-sm text-black" for="min_instances">Application Id</label>
          <Field name="application_id" v-slot="{ field }" :model-value="selected_applicationId">
            <Multiselect class="multi !bg-[#F9FAFB]" v-bind="field" v-model="selected_applicationId" placeholder="Search" :options="arrayOfRecords" :multiple="false" :maxHeight="200" label="appliName" track-by="appliId" :custom-label="appliId" @update:modelValue="trackChanges"></Multiselect>
          </Field>
          <ErrorMessage name="application_id" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
          </ErrorMessage>
        </div>
        <Button id="editPixelStreamingSettings" class="hidden" title="Submit" type="submit" theme="primary"></Button>
      </Form>
      <div v-if="isEdited"
        class="fixed bottom-0 left-0 w-full sm:left-[240px] sm:w-[calc(100%-240px)] z-50 bg-white dark:bg-bg-1000
              flex justify-end sm:justify-center items-center gap-3 px-8 py-4">
        <label for="editPixelStreamingSettings"
          class="bg-[#1A56DB] dark:bg-bg-1000 text-txt-1000 dark:text-txt-150
                rounded-lg flex flex-row justify-center items-center gap-[9px]
                px-4 h-10 m-0 cursor-pointer">
          Save
          <Spinner v-if="loader" />
        </label>
        <button type="reset"
          class="bg-gray-100 dark:bg-bg-1000 text-gray-500 dark:text-txt-150
                rounded-lg flex flex-row justify-center items-center gap-[9px]
                px-4 h-10 m-0 cursor-pointer"
          @click="handleCancel">
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
  background-color: #F9FAFB;
}

/* Copy */
.copyMsgBox {
  background-color: #323232;
  color: white;
  position: absolute;
  margin-top: 10px;
  padding: 10px;
  font-size: 14px;
  border-radius: 30px;
  transition: all 900ms linear;

}

.copyMsgBox::before {
  content: '';
  position: absolute;
  left: 22px;
  top: -5px;
  display: block;
  width: 15px;
  height: 15px;
  background-color: #323232;
  border-top-left-radius: 4px;
  transform: rotate(45deg);
}

</style>
