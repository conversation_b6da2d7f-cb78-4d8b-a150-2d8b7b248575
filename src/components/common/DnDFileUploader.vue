<script setup>
import { uploadFileSVG } from "@/helpers/constants";
import { useDropZone } from "@vueuse/core";
import { ref, defineProps, defineEmits, watch } from "vue";

const props = defineProps({
  inputType: {
    type: String,
    default: 'image/*',
  },
  inputPlaceholder: {
    type: String,
    default: '',
  },
  previousFileData: {
    type: Object,
    default: () => ({}),
  },
  onlyVideo: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['fileData']);
const filesData = ref([]);
const previewData = ref([]);
const previousData = ref(props.previousFileData);
const dropZoneRef = ref(null);
const fileInput = ref(null);
const showuploader = ref();

// Blob for preview
const processFiles = (files) => {

  return files.map((file) => {
    const blobURL = URL.createObjectURL(file);
    return {
      name: file.name,
      size: file.size,
      type: file.type,
      url: blobURL,
    };
  });
};

const onDrop = (files) => {
  if (files) {
    filesData.value = [files];
    previewData.value = processFiles(files);
    console.log("inside dnd drop", filesData.value[0]);
    emit('fileData', files[0]);
  }
};

const openStorage = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

const handleFileUpload = (event) => {
  const input = event.target;
  if (input.files) {
    filesData.value = [...Array.from(input.files)];
    previewData.value = processFiles(Array.from(input.files));
    console.log("inside dnd updaload", filesData.value[0]);
    emit('fileData', filesData.value[0]);
  }
};

const { isOverDropZone } = useDropZone(dropZoneRef, onDrop);

const removeImage = (index) => {
  previewData.value = previewData.value.filter((_, i) => i !== index);
};
const removePreviousImage = () => {
  showuploader.value = true;
  previousData.value = '';
};

watch(() => previewData.value, (val) => {
  previewData.value = val;
});

const extractFileName = (url) => {
  const decodedUrl = decodeURIComponent(url);
  const fileNameWithExtension = decodedUrl.split('/').pop();
  const fileName = fileNameWithExtension.replace(/_\d+(?=\.\w+$)/, '');
  return fileName.split('.')[0]; // Remove file extension
};

</script>

<template>
  <div class="flex flex-col gap-4">
    <!--Drag & Drop Zone + Click to Upload -->
    <div v-if="Object.keys(previousData).length === 0 && Object.keys(previewData).length === 0"
      ref="dropZoneRef"
      class="flex flex-col w-full h-full bg-[#F9FAFB] border-2 border-dashed border-gray-400 rounded-md justify-center items-center cursor-pointer"
      :class="{ 'bg-gray-300': isOverDropZone }"
      @click="openStorage"
    >
      <p class="text-gray-700 text-sm m-2 text-center">{{inputPlaceholder}}</p>
      <span v-if="!(inputType === 'ttf/*')" v-html="uploadFileSVG" class="bg-[#1C64F2] bg-opacity-20 p-2 rounded-full"></span>
      <p v-if="!(inputType === 'ttf/*')" class="text-gray-700 text-sm m-2 text-center">Browse</p>
      <p v-if="!(inputType === 'ttf/*')" class="text-gray-700 text-sm m-2 text-center">(Png , jpeg or webp)</p>
      <input
        ref="fileInput"
        type="file"
        style="display: none"
        multiple
        :accept="inputType"
        @change="handleFileUpload"
      />
    </div>

     <!--Display Uploaded Files -->
      <div v-if="previewData.length>0" class="flex flex-wrap gap-2" :class="inputType === 'ttf/*'?'w-full !h-[40px] border border-amber-200 rounded-lg':'w-full  !h-full'">
        <div v-for="(file, index) in previewData" :key="index" class="w-full h-full relative">
          <img   v-if="inputType ==='image/*'"  :src="file.url" alt="Uploaded Image" class="w-full h-full object-cover rounded-md" />
          <video v-else-if="inputType ==='video/*'" :src="file.url" controls class="w-full h-full object-cover rounded-md"></video>
          <p v-else-if="inputType === 'ttf/*'" class="flex justify-center items-center mt-2">{{  file.name }}</p>
          <div class="w-full h-full" v-else>
            <img v-if="file.type.startsWith('image/')" :src="file.url" alt="Uploaded Image" class="w-full h-full object-cover rounded-md" />
            <video v-else  :src="file.url" controls class="w-full h-full object-cover rounded-md"></video>
          </div>

          <!-- Delete button to remove image -->
          <div
            class="w-7 h-7 bg-white flex justify-center items-center rounded-lg absolute top-2 right-2 cursor-pointer"
            @click="removeImage(index)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
              <g clip-path="url(#clip0_713_6442)">
                <path d="M12.7044 2.94737H9.8525V1.47368C9.8525 1.08284 9.70227 0.708001 9.43486 0.431632C9.16745 0.155263 8.80476 0 8.42658 0L5.57473 0C5.19655 0 4.83386 0.155263 4.56644 0.431632C4.29903 0.708001 4.1488 1.08284 4.1488 1.47368V2.94737H1.29695C1.10786 2.94737 0.926513 3.025 0.792806 3.16318C0.6591 3.30137 0.583984 3.48879 0.583984 3.68421C0.583984 3.87963 0.6591 4.06705 0.792806 4.20524C0.926513 4.34342 1.10786 4.42105 1.29695 4.42105H2.00991V12.5263C2.00991 12.9172 2.16014 13.292 2.42755 13.5684C2.69497 13.8447 3.05766 14 3.43584 14H10.5655C10.9436 14 11.3063 13.8447 11.5737 13.5684C11.8412 13.292 11.9914 12.9172 11.9914 12.5263V4.42105H12.7044C12.8934 4.42105 13.0748 4.34342 13.2085 4.20524C13.3422 4.06705 13.4173 3.87963 13.4173 3.68421C13.4173 3.48879 13.3422 3.30137 13.2085 3.16318C13.0748 3.025 12.8934 2.94737 12.7044 2.94737ZM5.57473 1.47368H8.42658V2.94737H5.57473V1.47368ZM6.28769 11.0526C6.28769 11.2481 6.21257 11.4355 6.07887 11.5737C5.94516 11.7118 5.76381 11.7895 5.57473 11.7895C5.38564 11.7895 5.20429 11.7118 5.07058 11.5737C4.93688 11.4355 4.86176 11.2481 4.86176 11.0526V5.89474C4.86176 5.69931 4.93688 5.5119 5.07058 5.37371C5.20429 5.23553 5.38564 5.15789 5.57473 5.15789C5.76381 5.15789 5.94516 5.23553 6.07887 5.37371C6.21257 5.5119 6.28769 5.69931 6.28769 5.89474V11.0526ZM9.13954 11.0526C9.13954 11.2481 9.06442 11.4355 8.93072 11.5737C8.79701 11.7118 8.61567 11.7895 8.42658 11.7895C8.23749 11.7895 8.05614 11.7118 7.92244 11.5737C7.78873 11.4355 7.71361 11.2481 7.71361 11.0526V5.89474C7.71361 5.69931 7.78873 5.5119 7.92244 5.37371C8.05614 5.23553 8.23749 5.15789 8.42658 5.15789C8.61567 5.15789 8.79701 5.23553 8.93072 5.37371C9.06442 5.5119 9.13954 5.69931 9.13954 5.89474V11.0526Z" fill="#C81E1E"/>
              </g>
              <defs>
                <clipPath id="clip0_713_6442">
                  <rect width="14" height="14" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      </div>

    <!-- previous selected data preview -->
    <div  v-if="!onlyVideo && Object.keys(previousData).length > 0" class="flex flex-wrap gap-2 !w-full  !h-full relative" :class="inputType === 'ttf/*'?'w-full !h-[40px] border border-amber-200 rounded-lg':''">
          <img v-if="(previousData.type?.startsWith('image/') ||  previousData[1] === undefined) && !previousData.type?.startsWith('font/') "
            :src="previousData.url || previousData[0]?.url "
            alt="Image"
            class="w-full h-full object-cover rounded-md"
          />

         <video  v-if="previousData.length === 2 && previousData[1] !== undefined" class="w-full h-full object-cover rounded-md" controls
              :poster=" previousData[0] &&  previousData[0] !== undefined? previousData[0].url:''">
            <source :src="previousData[1] &&  previousData[1] !== undefined? previousData[1].url:''">
          </video>

          <p class="absolute w-fit left-10 top-2" v-if="previousData && previousData.type?.startsWith('font/') "> {{ extractFileName(previousData.name) }}</p>

           <!-- Delete button to remove image -- -->
          <div
            class="w-7 h-7 bg-white flex justify-center items-center rounded-lg absolute right-2 cursor-pointer"
            :class="inputType === 'ttf/*'?'top-1':'top-2'"
            @click="removePreviousImage()"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
              <g clip-path="url(#clip0_713_6442)">
                <path d="M12.7044 2.94737H9.8525V1.47368C9.8525 1.08284 9.70227 0.708001 9.43486 0.431632C9.16745 0.155263 8.80476 0 8.42658 0L5.57473 0C5.19655 0 4.83386 0.155263 4.56644 0.431632C4.29903 0.708001 4.1488 1.08284 4.1488 1.47368V2.94737H1.29695C1.10786 2.94737 0.926513 3.025 0.792806 3.16318C0.6591 3.30137 0.583984 3.48879 0.583984 3.68421C0.583984 3.87963 0.6591 4.06705 0.792806 4.20524C0.926513 4.34342 1.10786 4.42105 1.29695 4.42105H2.00991V12.5263C2.00991 12.9172 2.16014 13.292 2.42755 13.5684C2.69497 13.8447 3.05766 14 3.43584 14H10.5655C10.9436 14 11.3063 13.8447 11.5737 13.5684C11.8412 13.292 11.9914 12.9172 11.9914 12.5263V4.42105H12.7044C12.8934 4.42105 13.0748 4.34342 13.2085 4.20524C13.3422 4.06705 13.4173 3.87963 13.4173 3.68421C13.4173 3.48879 13.3422 3.30137 13.2085 3.16318C13.0748 3.025 12.8934 2.94737 12.7044 2.94737ZM5.57473 1.47368H8.42658V2.94737H5.57473V1.47368ZM6.28769 11.0526C6.28769 11.2481 6.21257 11.4355 6.07887 11.5737C5.94516 11.7118 5.76381 11.7895 5.57473 11.7895C5.38564 11.7895 5.20429 11.7118 5.07058 11.5737C4.93688 11.4355 4.86176 11.2481 4.86176 11.0526V5.89474C4.86176 5.69931 4.93688 5.5119 5.07058 5.37371C5.20429 5.23553 5.38564 5.15789 5.57473 5.15789C5.76381 5.15789 5.94516 5.23553 6.07887 5.37371C6.21257 5.5119 6.28769 5.69931 6.28769 5.89474V11.0526ZM9.13954 11.0526C9.13954 11.2481 9.06442 11.4355 8.93072 11.5737C8.79701 11.7118 8.61567 11.7895 8.42658 11.7895C8.23749 11.7895 8.05614 11.7118 7.92244 11.5737C7.78873 11.4355 7.71361 11.2481 7.71361 11.0526V5.89474C7.71361 5.69931 7.78873 5.5119 7.92244 5.37371C8.05614 5.23553 8.23749 5.15789 8.42658 5.15789C8.61567 5.15789 8.79701 5.23553 8.93072 5.37371C9.06442 5.5119 9.13954 5.69931 9.13954 5.89474V11.0526Z" fill="#C81E1E"/>
              </g>
              <defs>
                <clipPath id="clip0_713_6442">
                  <rect width="14" height="14" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
    </div>
    <div  v-if="onlyVideo && previousData" class="flex flex-wrap gap-2 !w-full  !h-full relative" :class="inputType === 'ttf/*'?'w-full !h-[40px] border border-amber-200 rounded-lg':''">
      <img v-if="previousData.type?.startsWith('image/')"
            :src="previousData?.url "
            alt="Image"
            class="w-full h-full object-cover rounded-md"
      />
      <video v-else-if="previousData.type?.startsWith('video/')" class="w-full h-full object-cover rounded-md" controls :poster="previousData.url">
        <source :src="previousData.url">
      </video>

      <div v-else class="flex items-center justify-between w-full px-3 py-2 border rounded bg-gray-100">
        {{ console.log(previousData.name) }}
        <span class="truncate text-sm font-medium text-gray-800">
          <span>{{ decodeURIComponent(previousData.name).split('/').pop() }}</span>
        </span>
      </div>

      <!-- Delete button to remove image -- -->
      <div class="w-7 h-7 bg-white flex justify-center items-center rounded-lg absolute right-2 cursor-pointer top-2" @click="removePreviousImage()">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
              <g clip-path="url(#clip0_713_6442)">
                <path d="M12.7044 2.94737H9.8525V1.47368C9.8525 1.08284 9.70227 0.708001 9.43486 0.431632C9.16745 0.155263 8.80476 0 8.42658 0L5.57473 0C5.19655 0 4.83386 0.155263 4.56644 0.431632C4.29903 0.708001 4.1488 1.08284 4.1488 1.47368V2.94737H1.29695C1.10786 2.94737 0.926513 3.025 0.792806 3.16318C0.6591 3.30137 0.583984 3.48879 0.583984 3.68421C0.583984 3.87963 0.6591 4.06705 0.792806 4.20524C0.926513 4.34342 1.10786 4.42105 1.29695 4.42105H2.00991V12.5263C2.00991 12.9172 2.16014 13.292 2.42755 13.5684C2.69497 13.8447 3.05766 14 3.43584 14H10.5655C10.9436 14 11.3063 13.8447 11.5737 13.5684C11.8412 13.292 11.9914 12.9172 11.9914 12.5263V4.42105H12.7044C12.8934 4.42105 13.0748 4.34342 13.2085 4.20524C13.3422 4.06705 13.4173 3.87963 13.4173 3.68421C13.4173 3.48879 13.3422 3.30137 13.2085 3.16318C13.0748 3.025 12.8934 2.94737 12.7044 2.94737ZM5.57473 1.47368H8.42658V2.94737H5.57473V1.47368ZM6.28769 11.0526C6.28769 11.2481 6.21257 11.4355 6.07887 11.5737C5.94516 11.7118 5.76381 11.7895 5.57473 11.7895C5.38564 11.7895 5.20429 11.7118 5.07058 11.5737C4.93688 11.4355 4.86176 11.2481 4.86176 11.0526V5.89474C4.86176 5.69931 4.93688 5.5119 5.07058 5.37371C5.20429 5.23553 5.38564 5.15789 5.57473 5.15789C5.76381 5.15789 5.94516 5.23553 6.07887 5.37371C6.21257 5.5119 6.28769 5.69931 6.28769 5.89474V11.0526ZM9.13954 11.0526C9.13954 11.2481 9.06442 11.4355 8.93072 11.5737C8.79701 11.7118 8.61567 11.7895 8.42658 11.7895C8.23749 11.7895 8.05614 11.7118 7.92244 11.5737C7.78873 11.4355 7.71361 11.2481 7.71361 11.0526V5.89474C7.71361 5.69931 7.78873 5.5119 7.92244 5.37371C8.05614 5.23553 8.23749 5.15789 8.42658 5.15789C8.61567 5.15789 8.79701 5.23553 8.93072 5.37371C9.06442 5.5119 9.13954 5.69931 9.13954 5.89474V11.0526Z" fill="#C81E1E"/>
              </g>
              <defs>
                <clipPath id="clip0_713_6442">
                  <rect width="14" height="14" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
    </div>
  </div>
</template>
