<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import { UserStore } from '../../../store/index';
import DesignMenuBar from '@/components/scenes/DesignMenuBar.vue';
import DesignSettingsIndex from '@/components/Projects/DesignSettings/designSettingsIndex.vue';

const userStore = UserStore();
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
            <DatacenterNavBar />
            <div class="h-full overflow-hidden w-full">
                <div v-if="userStore.user_data"
                    class="pt-0 bg-transparent h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                    <div class="flex h-full w-full overflow-hidden gap-1 border">
                        <designMenuBar />
                        <DesignSettingsIndex/>
                        <router-view></router-view>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
