import { SVG } from '@svgdotjs/svg.js';
// Type declaration

// styleObject-keys
interface StyleObject {
  styles: string;
}
// styleObject
interface ClassStyleMap {
  [className: string]: StyleObject;
}
// coordinateObject-keys
interface coordinatesObjectKeys {
  name?:string;
  g: string;
  x: number;
  y: number;
  width: number;
  height: number;
}
// coordinateObject
interface coordinatesObjectReference {
  [index: number]: coordinatesObjectKeys;
}

/* Methods */

function decodeILayerName (name:string) {
  // Only decode up to the first non-pattern part (i.e., stop after name portion)
  // regex to decode all _xNN_ sequences like _x20_ for space, _x2D_ for dash, etc.
  return name.replace(/_x([0-9A-Fa-f]{2})_/g, (_, hex) => {
    return String.fromCharCode(parseInt(hex, 16));
  });
}

export function decodeCleanLayerName (name:string) {
  const decoded = decodeILayerName(name);
  // Optional: Cut off extra hash if it starts after digits/underscore
  const cleaned =  decoded.replace(/(_\d{20,}_?)$/, '');
  console.log('cleaned', cleaned);
  return cleaned.replace(/_/g, ' '); // removed '_'
}

export async function validateSVGViewBox (file: File, expectedBoxSize: {width: number, height: number}): Promise<void>{
  return new Promise((resolve, reject) => {
    const reader = new FileReader(); // Built in API
    reader.onload = async function (e) {
      if (e.target && e.target.result){
        const parser = new DOMParser();
        const doc = parser.parseFromString(e.target.result as string, "image/svg+xml");
        const svgElement = doc.documentElement;
        const draw = SVG(svgElement);
        const svgViewBox = (draw as any).viewbox();
        if (!svgViewBox) {
          reject(new Error('SVG does not have a viewBox'));
        }

        if (svgViewBox.width === expectedBoxSize.width && svgViewBox.height === expectedBoxSize.height) {
          resolve();
        } else {
          reject(new Error(`ViewBox dimensions (${svgViewBox.width}*${svgViewBox.height}) do not match dzi dimensions (${expectedBoxSize.width}*${expectedBoxSize.height})`));
        }
      }
    };
    reader.onerror = reject; // Error handling
    reader.readAsText(file);
  });

}

async function extractTheUniqueClassAndStyles (styleContext: string): Promise<ClassStyleMap> {

  const classStyleRegex = /(\.[-\w]+(?:\s*,\s*\.[-\w]+)*)\s*\{([^}]*)\}/g; // Regex to match full class selectors and their styles

  const classStyleObjects: ClassStyleMap = {};
  let match: RegExpExecArray | null;

  // Find all class styles
  while ((match = classStyleRegex.exec(styleContext)) !== null) {
    // Note: No manual updation is not necessary for match this loop. Match returns the array of class, style etc.. which is returned by exec(). match[1] - considered as class and match[2] - considered as styles
    const selectors = match[1].split(',').map((s) => s.trim().substring(1)); // remove the whitespace and timout the class
    const styles = match[2].replace(/\s+/g, ' ').trim(); // remove the whitespace and timout
    selectors.forEach((className) => {
      if (classStyleObjects[className]) {
        classStyleObjects[className].styles += ';' + styles;        // existing
      } else {
        classStyleObjects[className] = { styles: styles }; // new
      }
    });
  }

  // Clean up styles
  for (const className in classStyleObjects) {
    // iterate through each key clean the styles values.
    if (Object.prototype.hasOwnProperty.call(classStyleObjects, className)) {
      const stylesArray = classStyleObjects[className].styles
        .split(';')
        .map((s) => s.trim())
        .filter((s) => s !== ''); // Remove empty styles and trimout
      const uniqueStyles = Array.from(new Set(stylesArray)); // Remove duplicates
      classStyleObjects[className].styles = uniqueStyles.join('; ') + ';';
    }
  }

  return classStyleObjects;

}

async function extractStylesAndUpdateInlineStyle (style: any, readSvgElement: any): Promise<any> {
  const fullStyleSheet = style.node;
  const listOfClassNamesAndStyles = await extractTheUniqueClassAndStyles(fullStyleSheet.textContent); // Get the class names from styles.
  const svgElement = readSvgElement;
  if (Object.keys(listOfClassNamesAndStyles).length > 0) {
    Object.keys(listOfClassNamesAndStyles).forEach((key) => {
      const className = key;
      if (svgElement.find(`.${className}`).length > 0){
        // find the element from svg file with matched class
        svgElement.find(`.${className}`).forEach((item: any) => {
          item.node.setAttribute('style', listOfClassNamesAndStyles[key]?.styles); // set via style inline
          item.node.removeAttribute('class'); // remvove the class
          item.node.setAttribute('data-name', key); // for cross reference
        });
      }
    });
  }

  return svgElement;
}

export async function GetCooridnatesFromReadSvgFile (
  file: File,
  src: string,
  attributesObject: { width: number; height: number } | null,
  bgImageHeightWidth: { width: number; height: number },
) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader(); // Built in API
    reader.onload = async function (e) {
      if (e.target && e.target.result){
        const parser = new DOMParser();
        const doc = parser.parseFromString(e.target.result as string, "image/svg+xml");
        const svgElement = doc.documentElement;
        let draw = SVG(svgElement);
        // Find style node
        if (draw.find('style').length > 0){
          const style = draw.find('style');
          draw = await extractStylesAndUpdateInlineStyle(style[0], draw);  // let consider the svg file has only one styleSheet.
        }
        const svgChildren = draw.children(); // Array of direct descendants children of svg element
        const coordinatesObject: coordinatesObjectReference = {}; // Object of coordinates
        if (svgChildren.length > 0) {
          svgChildren.forEach((item, index) => {
            if (item.type === "g"){
              // frame new Group
              const newGroup = SVG(
                `<g><g transform='translate(-${item.bbox().x}, -${
                  item.bbox().y
                })'>${item.node.innerHTML}</g></g>`);
              if (src === "library" && attributesObject) {
                coordinatesObject[index] = {
                  g: newGroup.node.innerHTML,
                  x: bgImageHeightWidth.width/2,
                  y: bgImageHeightWidth.height/2,
                  width: attributesObject.width,
                  height: attributesObject.height,
                };
              }  else {
                // custom coordinates
                const layerName = item.node.getAttribute('id');
                coordinatesObject[index] = {
                  g: newGroup.node.innerHTML,
                  ...(layerName && layerName !== null && {name: decodeCleanLayerName(layerName)}), // name
                  x: item.bbox().x,
                  y: item.bbox().y,
                  width: item.bbox().width,
                  height: item.bbox().height,
                };
              }
            }
          });
          resolve(JSON.stringify(coordinatesObject, null, 2));
        } else {
          reject("Invalid svg! svg has no children"); // Error handling
        }
      } else {
        reject("Failed to read file");
      }
    };
    reader.onerror = reject; // Error handling
    reader.readAsText(file);
  });
}

export async function GetHeightWidthFromReadSvgFile (file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader(); // Built in API
    console.log(reader);
    reader.onload = function (e) {
      if (e.target && e.target.result){
        const parser = new DOMParser();
        const doc = parser.parseFromString(e.target.result as string, "image/svg+xml");
        const svgElement = doc.documentElement;
        const draw = SVG(svgElement);
        const svgChildren = draw.children();
        let attributesObject = {};
        if (svgChildren.length > 0) {
          svgChildren.forEach((item) => {
            console.log(item);
            if (item.type === "g") {
              console.log(item.bbox().width);
              console.log(item.bbox().height);
              attributesObject = {
                width: item.bbox().width,
                height: item.bbox().height,
              };
            }
          });
          resolve(attributesObject);
        } else {
          reject("Invalid svg! svg has no children"); // Error handling
        }
      } else {
        reject("Failed to read file");
      }
    };
    reader.onerror = reject; // Error handling
    reader.readAsText(file);
  });
}
